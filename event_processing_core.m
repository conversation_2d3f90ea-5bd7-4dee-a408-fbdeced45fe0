function [adc_output, binary_output, stage_history, timing_info] = ...
    event_processing_core(Vin_p, Vin_n, t_sampled, event_system, ...
                         delay_buffers, rel_error_params, params)
%EVENT_PROCESSING_CORE 重构的事件驱动处理核心
%   修复时钟相位控制，确保正确的数据流时序同步
%   实现符合流水线ADC设计要求的时序控制

    num_samples = length(t_sampled);
    num_stages = params.num_stages;
    
    % 初始化输出
    adc_output = zeros(num_samples, 1);
    binary_output = zeros(num_samples, 10);
    stage_history = initialize_enhanced_stage_history(num_samples, num_stages);
    timing_info = struct('processed_events', 0, 'valid_outputs', 0, ...
                        'sha_updates', 0, 'pipeline_updates', 0, 'flash_updates', 0, ...
                        'clkh_events', 0, 'clks_events', 0);
    
    % 获取差分输入
    Vin_diff = (Vin_p - Vin_n) / 2;
    
    % 初始化流水线状态缓冲
    pipeline_state = initialize_pipeline_state(num_stages);
    
    fprintf('开始重构的事件驱动处理: %d个事件\n', event_system.num_events);
    
    % 处理每个事件 - 修复的时钟相位控制
    for event_idx = 1:event_system.num_events
        event = event_system.events(event_idx);
        sample_idx = event.index;
        
        % 确保索引在有效范围内
        if sample_idx <= 0 || sample_idx > num_samples
            continue;
        end
        
        % 修复的事件处理逻辑 - 正确的时钟相位分配
        switch event.type
            case 'CLKH_RISING'
                % CLKH上升沿处理:
                % 1. SHA进入采样态
                % 2. STAGE2,4,6,8进入采样态 (偶数级)
                [pipeline_state, stage_history, timing_info] = process_clkh_rising_fixed(...
                    pipeline_state, stage_history, timing_info, ...
                    Vin_diff(sample_idx), sample_idx, event.time, rel_error_params, params);
                timing_info.clkh_events = timing_info.clkh_events + 1;
                
            case 'CLKH_FALLING'
                % CLKH下降沿处理:
                % 1. SHA锁存并进入保持态
                % 2. STAGE2,4,6,8锁存并进入保持态 (偶数级)
                [pipeline_state, stage_history, timing_info] = process_clkh_falling_fixed(...
                    pipeline_state, stage_history, timing_info, ...
                    sample_idx, event.time, rel_error_params, params);
                
            case 'CLKS_RISING'
                % CLKS上升沿处理:
                % 1. STAGE1,3,5,7进入采样态 (奇数级)
                % 2. Flash ADC进入采样态
                [pipeline_state, stage_history, timing_info] = process_clks_rising_fixed(...
                    pipeline_state, stage_history, timing_info, ...
                    sample_idx, event.time, rel_error_params, params);
                timing_info.clks_events = timing_info.clks_events + 1;
                
            case 'CLKS_FALLING'
                % CLKS下降沿处理:
                % 1. STAGE1,3,5,7锁存并进入保持态 (奇数级)
                % 2. Flash ADC锁存并进入保持态
                % 3. 执行数字校正生成最终输出
                [pipeline_state, stage_history, adc_output, binary_output, timing_info] = ...
                    process_clks_falling_fixed(...
                    pipeline_state, stage_history, adc_output, binary_output, timing_info, ...
                    sample_idx, event.time, rel_error_params, params);
        end
        
        timing_info.processed_events = timing_info.processed_events + 1;
        
        % 每处理1000个事件显示进度
        if mod(timing_info.processed_events, 1000) == 0
            fprintf('已处理 %d/%d 事件 (CLKH:%d, CLKS:%d, SHA:%d, 流水线:%d)\n', ...
                timing_info.processed_events, event_system.num_events, ...
                timing_info.clkh_events, timing_info.clks_events, ...
                timing_info.sha_updates, timing_info.pipeline_updates);
        end
    end
    
    % 统计有效输出
    timing_info.valid_outputs = sum(abs(adc_output) > 0.001);
    
    fprintf('重构事件处理完成: %d个事件, %d个有效输出 (%.1f%%)\n', ...
        timing_info.processed_events, timing_info.valid_outputs, ...
        100*timing_info.valid_outputs/num_samples);
end

function pipeline_state = initialize_pipeline_state(num_stages)
%INITIALIZE_PIPELINE_STATE 初始化流水线状态缓冲
%   为每一级创建状态跟踪缓冲

    pipeline_state = struct();
    
    % SHA状态
    pipeline_state.sha = struct(...
        'input_data', 0, ...
        'latched_data', 0, ...
        'output_data', 0, ...
        'state', 'idle', ...        % 'sampling', 'holding', 'idle'
        'valid', false);
    
    % 各级流水线状态
    for stage = 1:num_stages
        pipeline_state.(['stage_', num2str(stage)]) = struct(...
            'input_data', 0, ...
            'latched_data', 0, ...
            'analog_output', 0, ...
            'digital_output', [0, 0], ...
            'state', 'idle', ...        % 'sampling', 'holding', 'idle'
            'valid_analog', false, ...
            'valid_digital', false);
    end
    
    % Flash ADC状态
    pipeline_state.flash = struct(...
        'input_data', 0, ...
        'latched_data', 0, ...
        'digital_output', [0, 0], ...
        'state', 'idle', ...
        'valid', false);
end

function [pipeline_state, stage_history, timing_info] = process_clkh_rising_fixed(...
    pipeline_state, stage_history, timing_info, input_value, sample_idx, timestamp, rel_error_params, params)
%PROCESS_CLKH_RISING_FIXED 修复的CLKH上升沿处理
%   SHA + 偶数级进入采样态

    num_stages = params.num_stages;
    
    % 1. SHA进入采样态
    pipeline_state.sha.state = 'sampling';
    pipeline_state.sha.input_data = input_value;
    timing_info.sha_updates = timing_info.sha_updates + 1;
    
    % 2. 偶数级 (STAGE2,4,6,8) 进入采样态
    for stage = 2:2:num_stages
        stage_field = ['stage_', num2str(stage)];
        prev_stage_field = ['stage_', num2str(stage-1)];
        
        % 从前一级获取数据 (前一级应该处于保持态)
        if pipeline_state.(prev_stage_field).valid_analog && ...
           strcmp(pipeline_state.(prev_stage_field).state, 'holding')
            
            pipeline_state.(stage_field).state = 'sampling';
            pipeline_state.(stage_field).input_data = pipeline_state.(prev_stage_field).analog_output;
            timing_info.pipeline_updates = timing_info.pipeline_updates + 1;
        end
    end
end

function [pipeline_state, stage_history, timing_info] = process_clkh_falling_fixed(...
    pipeline_state, stage_history, timing_info, sample_idx, timestamp, rel_error_params, params)
%PROCESS_CLKH_FALLING_FIXED 修复的CLKH下降沿处理
%   SHA + 偶数级锁存并进入保持态

    num_stages = params.num_stages;
    
    % 1. SHA锁存并进入保持态
    if strcmp(pipeline_state.sha.state, 'sampling')
        pipeline_state.sha.latched_data = pipeline_state.sha.input_data;
        pipeline_state.sha.output_data = pipeline_state.sha.latched_data;
        pipeline_state.sha.state = 'holding';
        pipeline_state.sha.valid = true;
        
        % 记录到历史
        stage_history.sha(sample_idx) = pipeline_state.sha.output_data;
    end
    
    % 2. 偶数级 (STAGE2,4,6,8) 锁存并进入保持态，同时执行ADC+MDAC
    for stage = 2:2:num_stages
        stage_field = ['stage_', num2str(stage)];
        
        if strcmp(pipeline_state.(stage_field).state, 'sampling')
            % 锁存输入数据
            pipeline_state.(stage_field).latched_data = pipeline_state.(stage_field).input_data;
            
            % 执行该级的ADC和MDAC处理
            [analog_out, digital_out] = process_pipeline_stage_fixed(...
                pipeline_state.(stage_field).latched_data, rel_error_params(stage, :), ...
                params, stage, timestamp);
            
            % 存储结果
            pipeline_state.(stage_field).analog_output = analog_out;
            pipeline_state.(stage_field).digital_output = digital_out;
            pipeline_state.(stage_field).state = 'holding';
            pipeline_state.(stage_field).valid_analog = true;
            pipeline_state.(stage_field).valid_digital = true;
            
            % 记录到历史
            stage_history.(['stage_', num2str(stage), '_analog'])(sample_idx) = analog_out;
            stage_history.(['stage_', num2str(stage), '_digital'])(sample_idx, :) = digital_out;
        end
    end
end

function [pipeline_state, stage_history, timing_info] = process_clks_rising_fixed(...
    pipeline_state, stage_history, timing_info, sample_idx, timestamp, rel_error_params, params)
%PROCESS_CLKS_RISING_FIXED 修复的CLKS上升沿处理
%   奇数级 + Flash ADC进入采样态

    num_stages = params.num_stages;
    
    % 1. STAGE1从SHA获取数据并进入采样态
    if pipeline_state.sha.valid && strcmp(pipeline_state.sha.state, 'holding')
        pipeline_state.stage_1.state = 'sampling';
        pipeline_state.stage_1.input_data = pipeline_state.sha.output_data;
        timing_info.pipeline_updates = timing_info.pipeline_updates + 1;
    end
    
    % 2. 其他奇数级 (STAGE3,5,7) 进入采样态
    for stage = 3:2:num_stages
        stage_field = ['stage_', num2str(stage)];
        prev_stage_field = ['stage_', num2str(stage-1)];
        
        % 从前一级获取数据 (前一级应该处于保持态)
        if pipeline_state.(prev_stage_field).valid_analog && ...
           strcmp(pipeline_state.(prev_stage_field).state, 'holding')
            
            pipeline_state.(stage_field).state = 'sampling';
            pipeline_state.(stage_field).input_data = pipeline_state.(prev_stage_field).analog_output;
            timing_info.pipeline_updates = timing_info.pipeline_updates + 1;
        end
    end
    
    % 3. Flash ADC从最后一级获取数据并进入采样态
    last_stage_field = ['stage_', num2str(num_stages)];
    if pipeline_state.(last_stage_field).valid_analog && ...
       strcmp(pipeline_state.(last_stage_field).state, 'holding')
        
        pipeline_state.flash.state = 'sampling';
        pipeline_state.flash.input_data = pipeline_state.(last_stage_field).analog_output;
        timing_info.flash_updates = timing_info.flash_updates + 1;
    end
end

function [pipeline_state, stage_history, adc_output, binary_output, timing_info] = ...
    process_clks_falling_fixed(...
    pipeline_state, stage_history, adc_output, binary_output, timing_info, ...
    sample_idx, timestamp, rel_error_params, params)
%PROCESS_CLKS_FALLING_FIXED 修复的CLKS下降沿处理
%   奇数级 + Flash ADC锁存并进入保持态，执行数字校正

    num_stages = params.num_stages;
    
    % 1. 奇数级 (STAGE1,3,5,7) 锁存并进入保持态，同时执行ADC+MDAC
    for stage = 1:2:num_stages
        stage_field = ['stage_', num2str(stage)];
        
        if strcmp(pipeline_state.(stage_field).state, 'sampling')
            % 锁存输入数据
            pipeline_state.(stage_field).latched_data = pipeline_state.(stage_field).input_data;
            
            % 执行该级的ADC和MDAC处理
            [analog_out, digital_out] = process_pipeline_stage_fixed(...
                pipeline_state.(stage_field).latched_data, rel_error_params(stage, :), ...
                params, stage, timestamp);
            
            % 存储结果
            pipeline_state.(stage_field).analog_output = analog_out;
            pipeline_state.(stage_field).digital_output = digital_out;
            pipeline_state.(stage_field).state = 'holding';
            pipeline_state.(stage_field).valid_analog = true;
            pipeline_state.(stage_field).valid_digital = true;
            
            % 记录到历史
            stage_history.(['stage_', num2str(stage), '_analog'])(sample_idx) = analog_out;
            stage_history.(['stage_', num2str(stage), '_digital'])(sample_idx, :) = digital_out;
        end
    end
    
    % 2. Flash ADC锁存并进入保持态
    if strcmp(pipeline_state.flash.state, 'sampling')
        pipeline_state.flash.latched_data = pipeline_state.flash.input_data;
        
        % 执行Flash ADC量化
        flash_digital = process_flash_adc(pipeline_state.flash.latched_data, params);
        
        pipeline_state.flash.digital_output = flash_digital;
        pipeline_state.flash.state = 'holding';
        pipeline_state.flash.valid = true;
        
        % 记录到历史
        stage_history.flash(sample_idx, :) = flash_digital;
    end
    
    % 3. 执行数字校正生成最终输出
    if all_stages_ready_for_correction(pipeline_state, num_stages)
        % 收集所有级的数字数据
        stage_digital_data = collect_stage_digital_data(pipeline_state, num_stages);
        
        % 执行数字校正
        [analog_result, binary_result] = perform_digital_correction_fixed(...
            stage_digital_data, params, timestamp);
        
        % 存储最终输出
        if ~isnan(analog_result)
            adc_output(sample_idx) = analog_result;
            binary_output(sample_idx, :) = binary_result;
            timing_info.valid_outputs = timing_info.valid_outputs + 1;
        end
    end
end

function [analog_out, digital_out] = process_pipeline_stage_fixed(...
    input_value, error_params, params, stage_num, timestamp)
%PROCESS_PIPELINE_STAGE_FIXED 修复的流水线级处理
%   执行1.5bit ADC量化和MDAC处理，使用完整传递函数

    % 1.5bit ADC量化 (阈值: ±Vref/4)
    Vref = params.Vref;
    threshold_low = -Vref/4;
    threshold_high = Vref/4;
    
    if input_value <= threshold_low
        digital_out = [0, 0];  % 00 -> -1
        D_value = -1;
    elseif input_value >= threshold_high
        digital_out = [1, 0];  % 10 -> +1
        D_value = 1;
    else
        digital_out = [0, 1];  % 01 -> 0
        D_value = 0;
    end
    
    % 完整MDAC传递函数: Vres = (1+a)*(2+b)*Vin - (1+a)*(1+b)*D*Vref
    % 从error_params中获取误差参数a和b
    if length(error_params) >= 2
        a = error_params(1);  % 第一个误差参数
        b = error_params(2);  % 第二个误差参数
    else
        a = 0;  % 理想状态
        b = 0;  % 理想状态
    end
    
    % 应用完整传递函数
    analog_out = (1+a)*(2+b)*input_value - (1+a)*(1+b)*D_value*Vref;
    
    % 限制输出范围
    analog_out = max(-Vref, min(Vref, analog_out));
end

function flash_digital = process_flash_adc(input_value, params)
%PROCESS_FLASH_ADC 处理Flash ADC
%   2bit Flash ADC，正确的量化阈值逻辑：[-Vref/2, 0, +Vref/2]

    Vref = params.Vref;
    
    % 根据用户澄清的量化逻辑：
    % 信号 < -1/2Vref → 00
    % -1/2Vref ≤ 信号 < 0 → 01  
    % 0 ≤ 信号 < 1/2Vref → 10
    % 信号 ≥ 1/2Vref → 11
    
    if input_value < -Vref/2
        flash_digital = [0, 0];      % 00
    elseif input_value < 0
        flash_digital = [0, 1];      % 01
    elseif input_value < Vref/2
        flash_digital = [1, 0];      % 10
    else
        flash_digital = [1, 1];      % 11
    end
end

function ready = all_stages_ready_for_correction(pipeline_state, num_stages)
%ALL_STAGES_READY_FOR_CORRECTION 检查是否所有级都准备好进行数字校正
%   确保所有级都有有效的数字输出

    ready = pipeline_state.flash.valid;
    
    for stage = 1:num_stages
        stage_field = ['stage_', num2str(stage)];
        ready = ready && pipeline_state.(stage_field).valid_digital;
    end
end

function stage_digital_data = collect_stage_digital_data(pipeline_state, num_stages)
%COLLECT_STAGE_DIGITAL_DATA 收集各级数字数据
%   为数字校正准备输入数据

    stage_digital_data = zeros(num_stages + 1, 2);  % +1 for Flash ADC
    
    % 收集流水线级数据
    for stage = 1:num_stages
        stage_field = ['stage_', num2str(stage)];
        stage_digital_data(stage, :) = pipeline_state.(stage_field).digital_output;
    end
    
    % 收集Flash ADC数据
    stage_digital_data(num_stages + 1, :) = pipeline_state.flash.digital_output;
end

function [analog_result, binary_result] = perform_digital_correction_fixed(...
    stage_digital_data, params, timestamp)
%PERFORM_DIGITAL_CORRECTION_FIXED 修复的数字校正
%   实现准确的18bit到10bit全加器累加逻辑

    num_stages = size(stage_digital_data, 1) - 1;  % 减去Flash ADC
    
    % 解码各级数据，按照要求映射为D0-D17
    % D0(STAGE1 MSB) -> D1(STAGE1 LSB) -> ... -> D16(Flash MSB) -> D17(Flash LSB)
    decoded_values = zeros(1, 18);  % D0到D17共18bit
    
    % 解码流水线级 (1.5bit解码) - D0到D15 (8级×2bit)
    % STAGE1产生D0(MSB)和D1(LSB)，STAGE8产生D14(MSB)和D15(LSB)
    for stage = 1:num_stages
        msb = stage_digital_data(stage, 1);
        lsb = stage_digital_data(stage, 2);
        
        % 每级产生2bit数据，STAGE1为高位，STAGE8为低位
        bit_idx_msb = (stage-1)*2 + 1;  % MSB位置：D0, D2, D4, ..., D14
        bit_idx_lsb = (stage-1)*2 + 2;  % LSB位置：D1, D3, D5, ..., D15
        
        decoded_values(bit_idx_msb) = msb;    % D0, D2, D4, ..., D14
        decoded_values(bit_idx_lsb) = lsb;    % D1, D3, D5, ..., D15
    end
    
    % 解码Flash ADC (2bit解码) - D16(MSB)和D17(LSB)
    flash_msb = stage_digital_data(num_stages + 1, 1);
    flash_lsb = stage_digital_data(num_stages + 1, 2);
    
    decoded_values(17) = flash_msb;  % D16 (Flash MSB)
    decoded_values(18) = flash_lsb;  % D17 (Flash LSB)
    
    % 执行精确的全加器累加逻辑
    binary_result = perform_precise_full_adder_accumulation(decoded_values);
    
    % DAC转换 (10bit到模拟值)
    analog_result = universal_dac_conversion(binary_result, params);
end

function binary_output = perform_precise_full_adder_accumulation(decoded_values)
%PERFORM_PRECISE_FULL_ADDER_ACCUMULATION 精确的全加器累加
%   按照要求8的精确累加过程：D17直接输出为B0，逐级累加

    binary_output = zeros(1, 10);  % B0到B9
    
    % D17(Flash LSB)直接输出为最终的10bit二进制码的最低位B0（第10位）
    binary_output(10) = decoded_values(18);  % D17 -> B0
    
    % 初始化进位
    carry = 0;
    
    % 按要求8的累加逻辑：
    % D16与D15相加，结果SUM输出为B1，进位作为下一级的进位输入
    [sum_val, carry] = universal_full_adder(decoded_values(17), decoded_values(16), 0);
    binary_output(9) = sum_val;  % B1（第9位）
    
    % D14与D13，以及D16和D15计算后的进位相加，结果SUM为B2
    [sum_val, carry] = universal_full_adder(decoded_values(15), decoded_values(14), carry);
    binary_output(8) = sum_val;  % B2（第8位）
    
    % D12与D11，以及上级进位相加，结果SUM为B3
    [sum_val, carry] = universal_full_adder(decoded_values(13), decoded_values(12), carry);
    binary_output(7) = sum_val;  % B3（第7位）
    
    % D10与D9，以及上级进位相加，结果SUM为B4
    [sum_val, carry] = universal_full_adder(decoded_values(11), decoded_values(10), carry);
    binary_output(6) = sum_val;  % B4（第6位）
    
    % D8与D7，以及上级进位相加，结果SUM为B5
    [sum_val, carry] = universal_full_adder(decoded_values(9), decoded_values(8), carry);
    binary_output(5) = sum_val;  % B5（第5位）
    
    % D6与D5，以及上级进位相加，结果SUM为B6
    [sum_val, carry] = universal_full_adder(decoded_values(7), decoded_values(6), carry);
    binary_output(4) = sum_val;  % B6（第4位）
    
    % D4与D3，以及上级进位相加，结果SUM为B7
    [sum_val, carry] = universal_full_adder(decoded_values(5), decoded_values(4), carry);
    binary_output(3) = sum_val;  % B7（第3位）
    
    % D2与D1，以及上级进位相加，结果SUM为B8
    [sum_val, carry] = universal_full_adder(decoded_values(3), decoded_values(2), carry);
    binary_output(2) = sum_val;  % B8（第2位）
    
    % D0与0（二进制0），以及上级进位相加，结果为B9（最高位）
    [sum_val, ~] = universal_full_adder(decoded_values(1), 0, carry);
    binary_output(1) = sum_val;  % B9（第1位，最高位）
    % 最后的进位丢弃
end

function stage_history = initialize_enhanced_stage_history(num_samples, num_stages)
%INITIALIZE_ENHANCED_STAGE_HISTORY 初始化增强的各级历史数据存储

    stage_history = struct();
    stage_history.sha = zeros(num_samples, 1);
    
    for stage = 1:num_stages
        stage_history.(['stage_', num2str(stage), '_analog']) = zeros(num_samples, 1);
        stage_history.(['stage_', num2str(stage), '_digital']) = zeros(num_samples, 2);
    end
    
    stage_history.flash = zeros(num_samples, 2);
    stage_history.valid_mask = false(num_samples, 1);
    
    % 增强的历史数据跟踪
    stage_history.timing_accuracy = zeros(num_samples, 1);
    stage_history.delay_alignment_status = false(num_samples, 1);
end

% ===== 通用函数封装 =====

function [sum_output, carry_output] = universal_full_adder(A, B, carry_input)
%UNIVERSAL_FULL_ADDER 通用全加器操作
%   实现标准全加器逻辑：A + B + Cin = SUM + Cout
%
% 输入参数:
%   A, B - 两个输入位 (0或1)
%   carry_input - 进位输入 (0或1)
%
% 输出参数:
%   sum_output - 和输出 (0或1)
%   carry_output - 进位输出 (0或1)

    % 标准全加器真值表实现
    total = A + B + carry_input;
    
    if total >= 2
        sum_output = total - 2;  % 余数作为和输出
        carry_output = 1;        % 产生进位
    else
        sum_output = total;      % 直接作为和输出
        carry_output = 0;        % 无进位
    end
    
    % 确保输出为0或1
    sum_output = mod(sum_output, 2);
    carry_output = mod(carry_output, 2);
end

function analog_output = universal_dac_conversion(binary_input, params)
%UNIVERSAL_DAC_CONVERSION 通用DAC转换函数
%   将10bit二进制码转换为模拟输出，实现要求8的完整转换过程
%
% 输入参数:
%   binary_input - 10bit二进制数组 [B9, B8, ..., B1, B0]
%   params - 参数结构体
%
% 输出参数:
%   analog_output - 转换后的模拟输出

    % 权重赋值：B9×512, B8×256, ..., B1×2, B0×1
    weights = [512, 256, 128, 64, 32, 16, 8, 4, 2, 1];
    
    % 将10位数字相加
    digital_sum = sum(binary_input .* weights);
    
    % 加上偏移量 offset = -(2^10-1)/2 = -511.5
    offset = -(2^10-1)/2;  % -511.5
    offset_value = digital_sum + offset;
    
    % 乘以1/512进行归一化操作
    normalized_value = offset_value / 512;
    
    % 映射到参考电压范围
    analog_output = normalized_value * params.Vref;
end

function [output_data, sample_hold_state] = universal_sample_hold(input_data, clock_state, previous_state)
%UNIVERSAL_SAMPLE_HOLD 通用采样保持函数
%   实现可复用的采样保持操作
%
% 输入参数:
%   input_data - 输入数据
%   clock_state - 当前时钟状态 ('sampling', 'holding', 'idle')
%   previous_state - 前一状态的数据结构
%
% 输出参数:
%   output_data - 输出数据
%   sample_hold_state - 采样保持状态信息

    switch clock_state
        case 'sampling'
            % 采样态：接收并暂存输入数据
            sample_hold_state.latched_data = input_data;
            sample_hold_state.state = 'sampling';
            output_data = 0;  % 采样态时输出为0V（无效输出）
            
        case 'holding'
            % 保持态：输出之前锁存的数据
            if isfield(previous_state, 'latched_data')
                output_data = previous_state.latched_data;
                sample_hold_state.latched_data = previous_state.latched_data;
            else
                output_data = 0;
                sample_hold_state.latched_data = 0;
            end
            sample_hold_state.state = 'holding';
            
        otherwise
            % 空闲态：输出为0
            output_data = 0;
            sample_hold_state.latched_data = 0;
            sample_hold_state.state = 'idle';
    end
    
    sample_hold_state.valid = (output_data ~= 0);
end

function [delayed_output, delay_state] = universal_delay_register(input_data, delay_cycles, time_info)
%UNIVERSAL_DELAY_REGISTER 通用T/2延迟寄存器
%   实现可复用的半周期延迟操作
%
% 输入参数:
%   input_data - 输入数据
%   delay_cycles - 延迟周期数 (以T/2为单位)
%   time_info - 时间信息结构体
%
% 输出参数:
%   delayed_output - 延迟后的输出
%   delay_state - 延迟状态信息

    % 简化的延迟实现（基于缓冲深度）
    buffer_depth = min(delay_cycles, 10);  % 限制最大缓冲深度
    
    % 模拟延迟缓冲操作
    if buffer_depth > 0
        % 实际实现中这里应该维护一个循环缓冲区
        delayed_output = input_data;  % 简化实现
        delay_state.actual_delay = buffer_depth;
    else
        delayed_output = input_data;
        delay_state.actual_delay = 0;
    end
    
    delay_state.target_delay = delay_cycles;
    delay_state.efficiency = 1.0;  % 延迟效率
end

function [digital_out, quantization_info] = universal_1p5bit_adc(input_value, threshold_low, threshold_high)
%UNIVERSAL_1P5BIT_ADC 通用1.5bit ADC量化函数
%   实现可复用的1.5bit量化操作
%
% 输入参数:
%   input_value - 输入模拟值
%   threshold_low - 低阈值
%   threshold_high - 高阈值
%
% 输出参数:
%   digital_out - 2bit数字输出 [MSB, LSB]
%   quantization_info - 量化信息

    if input_value <= threshold_low
        digital_out = [0, 0];  % 00 -> -1
        D_value = -1;
        region = 'low';
    elseif input_value >= threshold_high
        digital_out = [1, 0];  % 10 -> +1
        D_value = 1;
        region = 'high';
    else
        digital_out = [0, 1];  % 01 -> 0
        D_value = 0;
        region = 'mid';
    end
    
    quantization_info.D_value = D_value;
    quantization_info.region = region;
    quantization_info.input_level = input_value;
end

function analog_residue = universal_mdac(input_value, digital_code, error_params, Vref)
%UNIVERSAL_MDAC 通用MDAC处理函数
%   实现完整的MDAC传递函数
%
% 输入参数:
%   input_value - 输入模拟值
%   digital_code - 量化后的数字码信息
%   error_params - 误差参数 [a, b]
%   Vref - 参考电压
%
% 输出参数:
%   analog_residue - MDAC输出的残差信号

    % 获取误差参数
    if length(error_params) >= 2
        a = error_params(1);
        b = error_params(2);
    else
        a = 0;  % 理想状态
        b = 0;  % 理想状态
    end
    
    % 获取D值
    if isfield(digital_code, 'D_value')
        D_value = digital_code.D_value;
    else
        D_value = 0;
    end
    
    % 完整MDAC传递函数: Vres = (1+a)*(2+b)*Vin - (1+a)*(1+b)*D*Vref
    analog_residue = (1+a)*(2+b)*input_value - (1+a)*(1+b)*D_value*Vref;
    
    % 限制输出范围
    analog_residue = max(-Vref, min(Vref, analog_residue));
end

% ===== 缺失的辅助函数 =====

function delay_index = calculate_precise_delay_buffer_index(required_delay_half_periods, timestamp, params)
%CALCULATE_PRECISE_DELAY_BUFFER_INDEX 计算精确延迟缓冲索引
%   根据所需延迟和当前时间戳计算缓冲区索引

    % 基于延迟规格计算索引
    delay_index = max(1, required_delay_half_periods);
    
    % 应用时间戳校正（可选，用于高精度时序）
    if abs(timestamp) > 1e-12
        % 时序微调（当前简化为直接使用延迟规格）
        delay_index = delay_index;
    end
    
    % 确保索引在合理范围内
    delay_index = max(1, min(delay_index, 10));
end

function [Vin_p, Vin_n, t_sampled, clks_input, clkh_input] = validate_inputs(...
    Vin_p, Vin_n, t_sampled, clks_input, clkh_input)
%VALIDATE_INPUTS 验证和格式化输入参数

    Vin_p = Vin_p(:);
    Vin_n = Vin_n(:);
    t_sampled = t_sampled(:);
    clks_input = clks_input(:);
    clkh_input = clkh_input(:);
    
    if length(Vin_p) ~= length(t_sampled) || length(clks_input) ~= length(t_sampled)
        error('所有输入向量长度必须一致');
    end
end 
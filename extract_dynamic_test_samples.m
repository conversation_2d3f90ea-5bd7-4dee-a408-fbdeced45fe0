function [sampled_data, sampling_info] = extract_dynamic_test_samples(...
    full_adc_output, time_vector, clks, clkh, params)
%EXTRACT_DYNAMIC_TEST_SAMPLES 智能动态特性采样
%   从完整流水线ADC输出中智能提取FFT分析用的稳定状态样本
%   
%   核心策略：
%   1. 使用完整156,527个样本进行事件驱动处理（保持时序精度）
%   2. 从稳定区域按时钟周期75%处智能提取1024个样本
%   3. 避免启动周期，确保信号稳定性
%
%   输入参数:
%       full_adc_output - 完整的流水线ADC输出 (156,527个样本)
%       time_vector - 对应的时间向量
%       clks, clkh - 时钟信号
%       params - ADC参数结构体
%
%   输出参数:
%       sampled_data - 提取的1024个样本用于FFT分析
%       sampling_info - 采样信息和质量报告

    fprintf('=== 智能动态特性采样 ===\n');
    fprintf('输入样本数: %d\n', length(full_adc_output));
    
    % 第一步：检测信号稳定区域
    [stable_region, stability_info] = detect_stable_region(full_adc_output, time_vector, params);
    
    % 第二步：检测时钟周期
    [clock_cycles, cycle_info] = detect_clock_cycles(time_vector, clks, clkh, params);
    
    % 第三步：在稳定区域的时钟周期75%处进行智能采样
    [sampled_data, sample_indices] = intelligent_sampling_at_75_percent(...
        full_adc_output, time_vector, stable_region, clock_cycles, params);
    
    % 第四步：质量验证和报告
    sampling_info = generate_sampling_report(sampled_data, sample_indices, ...
                                            stability_info, cycle_info, params);
    
    fprintf('智能采样完成: %d个样本提取成功\n', length(sampled_data));
    fprintf('采样质量评分: %.1f/10\n', sampling_info.quality_score);
end

function [stable_region, stability_info] = detect_stable_region(adc_output, time_vector, params)
%DETECT_STABLE_REGION 检测信号稳定区域
%   跳过启动周期，找到信号稳定的区域

    % 计算信号周期和启动周期数
    signal_period = 1 / params.f_in;
    startup_cycles = 5;  % 跳过前5个周期
    stable_cycles = 20;  % 使用后20个稳定周期
    
    % 计算稳定区域时间范围
    stable_start_time = startup_cycles * signal_period;
    stable_end_time = (startup_cycles + stable_cycles) * signal_period;
    
    % 找到稳定区域的索引
    stable_mask = (time_vector >= stable_start_time) & (time_vector <= stable_end_time);
    stable_indices = find(stable_mask);
    
    % 进一步筛选有效数据点
    stable_data = adc_output(stable_indices);
    valid_mask = abs(stable_data) > 0.001;  % 排除无效的零值点
    final_stable_indices = stable_indices(valid_mask);
    
    stable_region = struct();
    stable_region.indices = final_stable_indices;
    stable_region.start_time = stable_start_time;
    stable_region.end_time = stable_end_time;
    stable_region.data = adc_output(final_stable_indices);
    
    % 稳定性评估
    stability_info = struct();
    stability_info.total_samples = length(final_stable_indices);
    stability_info.coverage_ratio = length(final_stable_indices) / length(stable_indices);
    stability_info.signal_std = std(stable_region.data);
    stability_info.signal_range = [min(stable_region.data), max(stable_region.data)];
    
    fprintf('稳定区域检测: %d个有效样本 (覆盖率%.1f%%)\n', ...
        stability_info.total_samples, stability_info.coverage_ratio * 100);
end

function [clock_cycles, cycle_info] = detect_clock_cycles(time_vector, clks, clkh, params)
%DETECT_CLOCK_CYCLES 检测时钟周期
%   分析时钟信号，找到每个周期的关键时间点

    % 检测主时钟上升沿（CLKS）
    clks_rising_edges = find(diff([0; clks]) > 0.5);
    clks_periods = [];
    
    % 计算每个时钟周期的时间范围
    for i = 1:length(clks_rising_edges)-1
        period_start = time_vector(clks_rising_edges(i));
        period_end = time_vector(clks_rising_edges(i+1));
        period_duration = period_end - period_start;
        
        % 计算75%时间点
        percent_75_time = period_start + 0.75 * period_duration;
        
        clks_periods = [clks_periods; struct(...
            'start_time', period_start, ...
            'end_time', period_end, ...
            'duration', period_duration, ...
            'percent_75_time', percent_75_time, ...
            'start_index', clks_rising_edges(i), ...
            'end_index', clks_rising_edges(i+1))];
    end
    
    clock_cycles = clks_periods;
    
    % 周期信息统计
    cycle_info = struct();
    cycle_info.total_cycles = length(clock_cycles);
    cycle_info.avg_period = mean([clock_cycles.duration]);
    cycle_info.period_std = std([clock_cycles.duration]);
    cycle_info.frequency_measured = 1 / cycle_info.avg_period;
    
    fprintf('时钟周期检测: %d个周期，平均周期%.2fns\n', ...
        cycle_info.total_cycles, cycle_info.avg_period * 1e9);
end

function [sampled_data, sample_indices] = intelligent_sampling_at_75_percent(...
    adc_output, time_vector, stable_region, clock_cycles, params)
%INTELLIGENT_SAMPLING_AT_75_PERCENT 在时钟周期75%处智能采样
%   确保提取1024个高质量样本用于FFT分析

    target_samples = 1024;  % FFT分析目标样本数
    sample_indices = [];
    sampled_data = [];
    
    % 筛选稳定区域内的时钟周期
    stable_cycles = [];
    for i = 1:length(clock_cycles)
        cycle = clock_cycles(i);
        if cycle.percent_75_time >= stable_region.start_time && ...
           cycle.percent_75_time <= stable_region.end_time
            stable_cycles = [stable_cycles; cycle];
        end
    end
    
    fprintf('稳定区域内可用周期: %d个\n', length(stable_cycles));
    
    % 计算采样策略
    if length(stable_cycles) >= target_samples
        % 周期数充足，每个周期采样1个点
        cycle_step = floor(length(stable_cycles) / target_samples);
        selected_cycles = stable_cycles(1:cycle_step:end);
        if length(selected_cycles) > target_samples
            selected_cycles = selected_cycles(1:target_samples);
        end
    else
        % 周期数不足，需要多重采样策略
        samples_per_cycle = ceil(target_samples / length(stable_cycles));
        selected_cycles = repmat(stable_cycles, samples_per_cycle, 1);
        if length(selected_cycles) > target_samples
            selected_cycles = selected_cycles(1:target_samples);
        end
    end
    
    % 在每个选定周期的75%处进行采样
    for i = 1:length(selected_cycles)
        cycle = selected_cycles(i);
        target_time = cycle.percent_75_time;
        
        % 找到最接近75%时间点的索引
        [~, closest_idx] = min(abs(time_vector - target_time));
        
        % 确保该点在稳定区域内且有有效数据
        if ismember(closest_idx, stable_region.indices) && ...
           abs(adc_output(closest_idx)) > 0.001
            sample_indices = [sample_indices; closest_idx];
            sampled_data = [sampled_data; adc_output(closest_idx)];
        end
    end
    
    % 确保达到目标样本数
    if length(sampled_data) < target_samples
        fprintf('警告: 只提取到%d个样本，少于目标%d个\n', ...
            length(sampled_data), target_samples);
        
        % 补充策略：从稳定区域随机补充
        needed_samples = target_samples - length(sampled_data);
        available_indices = setdiff(stable_region.indices, sample_indices);
        
        if length(available_indices) >= needed_samples
            additional_indices = available_indices(1:needed_samples);
            sample_indices = [sample_indices; additional_indices];
            sampled_data = [sampled_data; adc_output(additional_indices)];
        end
    elseif length(sampled_data) > target_samples
        % 截断到目标样本数
        sample_indices = sample_indices(1:target_samples);
        sampled_data = sampled_data(1:target_samples);
    end
    
    fprintf('最终采样结果: %d个样本\n', length(sampled_data));
end

function sampling_info = generate_sampling_report(sampled_data, sample_indices, ...
                                                 stability_info, cycle_info, params)
%GENERATE_SAMPLING_REPORT 生成采样质量报告

    sampling_info = struct();
    
    % 基础统计
    sampling_info.num_samples = length(sampled_data);
    sampling_info.sample_indices = sample_indices;
    sampling_info.data_mean = mean(sampled_data);
    sampling_info.data_std = std(sampled_data);
    sampling_info.data_range = [min(sampled_data), max(sampled_data)];
    
    % 质量评估指标
    sampling_info.stability_score = min(10, 10 - stability_info.signal_std * 100);
    sampling_info.coverage_score = stability_info.coverage_ratio * 10;
    sampling_info.frequency_accuracy = min(10, 10 - abs(cycle_info.frequency_measured - params.fs) / params.fs * 100);
    
    % 综合质量评分
    sampling_info.quality_score = mean([sampling_info.stability_score, ...
                                      sampling_info.coverage_score, ...
                                      sampling_info.frequency_accuracy]);
    
    % 相干采样验证
    N = sampling_info.num_samples;
    M = round(params.f_in * N / params.fs);
    coherent_error = abs(M - (params.f_in * N / params.fs));
    sampling_info.coherent_sampling = coherent_error < 0.01;
    sampling_info.coherent_error = coherent_error;
    
    fprintf('采样质量报告:\n');
    fprintf('  样本数: %d\n', sampling_info.num_samples);
    fprintf('  稳定性评分: %.1f/10\n', sampling_info.stability_score);
    fprintf('  覆盖率评分: %.1f/10\n', sampling_info.coverage_score);
    fprintf('  频率精度评分: %.1f/10\n', sampling_info.frequency_accuracy);
    fprintf('  相干采样: %s (误差: %.6f)\n', ...
        iif(sampling_info.coherent_sampling, '满足', '不满足'), ...
        sampling_info.coherent_error);
end

function result = iif(condition, true_value, false_value)
%IIF 简化的条件表达式
    if condition
        result = true_value;
    else
        result = false_value;
    end
end 
%% 流水线ADC模型完整功能验证主脚本
% 验证模型是否完全符合用户设计思路与要求
% 重点：理想状态、两种模拟输出、精确时序控制
% VERSION HISTORY:
% v1: 原始版本 - 基础功能验证
% v2: 增加v2版本事件驱动核心支持
% v3: 集成v3版本事件处理核心，优化理想ADC对比
% v4: 完全重构版本 - 回归实际电路设计思路
% v5: 简化波形对比功能，移除复杂对齐和偏差计算逻辑

clear; clc; close all;

fprintf('=== 流水线ADC模型完整功能验证 (v5简化版本) ===\n');

%% 设置测试参数
params = struct();
params.Vref = 1.0;                % 参考电压
params.num_stages = 8;            % 8级流水线
params.fs = 100e6;               % 100MHz采样频率
params.resolution = 10;           % 10位分辨率
params.Vcm = 0;                   % 共模电压
params.f_in = 23/1024*100e6;            % 输入正弦波频率 (符合相干采样要求)
params.num_sine_cycles = 30;       % 30个正弦波周期
params.num_cycles = 30;             % 与num_sine_cycles一致

fprintf('ADC参数设置完成:\n');
fprintf('  流水线级数: %d级\n', params.num_stages);
fprintf('  采样频率: %.1f MHz\n', params.fs/1e6);
fprintf('  输入频率: %.3f MHz\n', params.f_in/1e6);
fprintf('  分辨率: %d bit\n', params.resolution);
fprintf('  总周期数: %d个\n', params.num_cycles);

%% 设置误差参数矩阵（零误差理想验证）
% 新格式：8×3矩阵 [gain_error, cap_mismatch, offset_error]
% 8行对应8级流水线，3列对应3种误差参数
rel_error_params = zeros(params.num_stages, 3);

% 非零误差参数设置示例（注释掉，用于参考）：
% rel_error_params = [
%     % Stage1: [gain_error, cap_mismatch, offset_error]
%     0.01,  0.005,  0.001;  % 1%增益误差, 0.5%电容失配, 1mV偏移
%     0.01,  0.005,  0.001;  % Stage2
%     0.01,  0.005,  0.001;  % Stage3
%     0.01,  0.005,  0.001;  % Stage4
%     0.01,  0.005,  0.001;  % Stage5
%     0.01,  0.005,  0.001;  % Stage6
%     0.01,  0.005,  0.001;  % Stage7
%     0.01,  0.005,  0.001   % Stage8
% ];

fprintf('\n误差参数矩阵设置完成:\n');
fprintf('  矩阵尺寸: %d×%d (8级流水线×3种误差)\n', size(rel_error_params));
fprintf('  误差类型: [增益误差, 电容失配误差, 偏移误差]\n');
fprintf('  误差值: 全零 (理想验证模式)\n');
fprintf('  备注: 如需测试非理想情况，请取消注释非零误差参数设置示例\n');

%% 初始化变量
clks = [];
clkh = [];
t_sampled = [];

%% 步骤1: 验证固定时间步长信号生成 (重构架构)
fprintf('\n[步骤1/4] 验证固定时间步长信号生成 (重构架构)...\n');

try
    % 使用新的generate_timebase固定时间步长生成器
    [t_sampled, sine_wave, clks, clkh, digital_data_matrix, analog_data_matrix] = ...
        generate_timebase(params);
    
    % 检查是否成功生成信号
    if isempty(t_sampled) || isempty(sine_wave) || isempty(clks) || isempty(clkh)
        error('信号生成失败：未能生成有效的输入波形或时钟信号');
    end
    
    fprintf('✓ 固定时间步长信号生成成功 (重构架构)\n');
    fprintf('  总样本数: %d\n', length(t_sampled));
    fprintf('  时间范围: [%.6f, %.6f]s\n', min(t_sampled), max(t_sampled));
    fprintf('  时间步长: 10 ps (固定)\n');
    fprintf('  数据矩阵: 数字%s, 模拟%s\n', ...
            mat2str(size(digital_data_matrix)), mat2str(size(analog_data_matrix)));
    
    % 验证非交叠时钟
    overlap_check = any((clks == 1) & (clkh == 1));
    if ~overlap_check
        fprintf('  非交叠时钟验证: ✓ 通过\n');
    else
        fprintf('  非交叠时钟验证: ✗ 检测到重叠\n');
    end
    
    step1_success = true;
    
catch ME
    fprintf('✗ 固定时间步长信号生成失败: %s\n', ME.message);
    step1_success = false;
end

%% 步骤2: 验证v4版本事件驱动流水线ADC处理
if step1_success
    fprintf('\n[步骤2/4] 验证v4版本事件驱动流水线ADC处理...\n');
    
    try
        % 创建差分输入信号
        Vin_p = sine_wave;
        Vin_n = zeros(size(sine_wave));  % 单端转差分
        
        % 使用event_driven_pipeline_adc_v4集成v6事件核心和v4数字校正
        fprintf('使用v4重构版流水线ADC模型(含v6事件核心+v4数字校正)...\n');
        fprintf('误差参数: 8×3零误差矩阵 (理想验证模式)\n');
        [final_dac_output, binary_output, processing_info, correction_metrics] = ...
            event_driven_pipeline_adc_v4(Vin_p, Vin_n, params, rel_error_params, t_sampled, clks, clkh);
        
        % 验证最终输出
        final_valid = sum(abs(final_dac_output) > 0.001);
        binary_valid = sum(any(binary_output ~= 0, 2));
        
        fprintf('✓ v4版本事件驱动流水线ADC(重构架构)处理完成\n');
        fprintf('  最终DAC模拟输出有效样本: %d/%d (%.1f%%)\n', ...
                final_valid, length(final_dac_output), 100*final_valid/length(final_dac_output));
        fprintf('  10位二进制输出有效样本: %d/%d (%.1f%%)\n', ...
                binary_valid, size(binary_output,1), 100*binary_valid/size(binary_output,1));
        
        % 重构架构统计信息
        fprintf('  处理事件数: %d\n', processing_info.total_events);
        fprintf('  有效输出事件: %d\n', processing_info.final_analog_outputs);
        fprintf('  整体效率: %.1f%%\n', 100*processing_info.overall_efficiency);
        fprintf('  数字校正效率: %.1f%%\n', 100*correction_metrics.processing_efficiency);

        step2_success = (final_valid > 0);
        
        % 创建兼容性结构（用于后续步骤）
        timing_info = struct();
        timing_info.processed_events = processing_info.total_events;
        timing_info.valid_outputs = processing_info.final_analog_outputs;
        timing_info.processing_efficiency = processing_info.overall_efficiency;
        timing_info.data_alignment_quality = correction_metrics.processing_efficiency;
        timing_info.model_version = 'v4.0_reconstructed';
        
        % 创建stage_history兼容结构（用于可视化）
        stage_history = struct();
        stage_history.sha = zeros(size(final_dac_output));  % v6不单独输出SHA
        stage_history.stage_1_analog = zeros(size(final_dac_output));  % v6不单独输出各级
        stage_history.stage_1_digital = zeros(length(final_dac_output), 2);  % v6统一处理
        
    catch ME
        fprintf('✗ v4版本事件处理核心处理失败: %s\n', ME.message);
        step2_success = false;
    end
else
    step2_success = false;
    fprintf('\n[步骤2/4] 跳过v4版本ADC处理测试\n');
end

%% 步骤3: 验证简化理想ADC对比 (v5简化版本)
if step1_success
    fprintf('\n[步骤3/4] 验证简化理想ADC对比 (v5简化版本)...\n');
    
    try
        % 生成理想ADC参考 - 使用原版时钟信号格式
        % 从原版时钟信号提取CLKH高电平作为理想ADC采样时刻
        ideal_sampling_indices = find(clkh == 1);
        [ideal_output, ~, ~, ~] = run_ideal_adc_v2(0, params, t_sampled, clkh);
        
        ideal_valid = sum(abs(ideal_output) > 0.001);
        fprintf('✓ 理想ADC参考生成成功 (基于CLKH上升沿)\n');
        fprintf('  理想ADC有效输出: %d\n', ideal_valid);
        fprintf('  采样时刻数: %d\n', length(ideal_sampling_indices));
        
        if step2_success && ideal_valid > 0
            % 简化的波形对比分析
            fprintf('启动简化的波形对比分析...\n');
            
            % 存储对比数据用于可视化
            comparison_data = struct();
            comparison_data.pipeline_output = final_dac_output;
            comparison_data.ideal_output = ideal_output;
            comparison_data.ideal_sampling_indices = ideal_sampling_indices;
            
            fprintf('✓ 简化波形对比分析完成\n');
            fprintf('  流水线ADC有效输出: %d\n', sum(abs(final_dac_output) > 0.001));
            fprintf('  理想ADC有效输出: %d\n', ideal_valid);
            fprintf('  数据已准备用于图形化对比\n');
            
        else
            fprintf('! 无法进行对比：流水线ADC或理想ADC输出无效\n');
        end
        
        step3_success = true;
        
    catch ME
        fprintf('✗ 简化理想ADC对比失败: %s\n', ME.message);
        step3_success = false;
    end
else
    step3_success = false;
    fprintf('\n[步骤3/4] 跳过简化理想ADC对比\n');
end

%% v5版本可视化功能
fprintf('\n[可视化] 生成v5版本简化波形对比图...\n');

try
    % 可视化1: 简化的理想ADC vs 流水线ADC对比图
    figure('Name', 'Simplified Pipeline ADC vs Ideal ADC Comparison', 'Position', [100, 100, 1400, 1000]);
    
    % 输入信号
    subplot(2, 3, 1);
    plot_samples = min(2000, length(t_sampled));
    if exist('sine_wave', 'var') && ~isempty(sine_wave)
        plot(t_sampled(1:plot_samples)*1e6, sine_wave(1:plot_samples), 'b-', 'LineWidth', 1.5);
        title('Input Sine Wave Signal');
    else
        % 生成默认正弦波用于显示
        t_display = t_sampled(1:plot_samples);
        sine_display = params.Vref * sin(2*pi*params.f_in*t_display);
        plot(t_display*1e6, sine_display, 'b--', 'LineWidth', 1.5);
        title('Input Sine Wave Signal (Generated for Display)');
    end
    xlabel('Time (μs)');
    ylabel('Amplitude (V)');
    grid on;
    
    % 简化的ADC输出对比
    subplot(2, 3, 2);
    if step3_success && exist('comparison_data', 'var') && ~isempty(comparison_data)
        % 显示理想ADC输出
        ideal_valid_idx = find(abs(comparison_data.ideal_output) > 0.001);
        if ~isempty(ideal_valid_idx)
            display_count = min(1000, length(ideal_valid_idx));
            plot(t_sampled(ideal_valid_idx(1:display_count))*1e6, ...
                 comparison_data.ideal_output(ideal_valid_idx(1:display_count)), ...
                 'g.-', 'MarkerSize', 6, 'LineWidth', 2, 'DisplayName', 'Ideal ADC');
        end
        
        % 显示流水线ADC输出
        hold on;
        pipeline_valid_idx = find(abs(comparison_data.pipeline_output) > 0.001);
        if ~isempty(pipeline_valid_idx)
            display_count = min(1000, length(pipeline_valid_idx));
            plot(t_sampled(pipeline_valid_idx(1:display_count))*1e6, ...
                 comparison_data.pipeline_output(pipeline_valid_idx(1:display_count)), ...
                 'r.-', 'MarkerSize', 4, 'LineWidth', 1, 'DisplayName', 'Pipeline ADC');
        end
        hold off;
        
        xlabel('Time (μs)');
        ylabel('Amplitude (V)');
        title('ADC Outputs Comparison');
        legend('show', 'Location', 'best');
        grid on;
    else
        text(0.5, 0.5, 'Comparison Data Not Available', 'HorizontalAlignment', 'center');
        title('ADC Output Comparison (No Data)');
    end
    
    % 输出数据统计
    subplot(2, 3, 3);
    if step3_success && exist('comparison_data', 'var')
        ideal_count = sum(abs(comparison_data.ideal_output) > 0.001);
        pipeline_count = sum(abs(comparison_data.pipeline_output) > 0.001);
        total_samples = length(t_sampled);
        
        counts = [ideal_count, pipeline_count];
        labels = {'Ideal ADC', 'Pipeline ADC'};
        
        bar(counts, 'FaceColor', [0.2, 0.6, 0.8]);
        set(gca, 'XTickLabel', labels);
        ylabel('Valid Sample Count');
        title('Valid Output Samples');
        grid on;
        
        % 添加数值标签
        for i = 1:length(counts)
            text(i, counts(i) + max(counts)*0.02, sprintf('%d (%.1f%%)', counts(i), 100*counts(i)/total_samples), ...
                 'HorizontalAlignment', 'center', 'FontWeight', 'bold');
        end
    end
    
    % 时钟信号
    subplot(2, 3, 4);
    clock_samples = min(1000, length(t_sampled));
    plot(t_sampled(1:clock_samples)*1e6, clks(1:clock_samples), 'r-', 'LineWidth', 1.5, 'DisplayName', 'CLKS');
    hold on;
    plot(t_sampled(1:clock_samples)*1e6, clkh(1:clock_samples) + 1.2, 'b-', 'LineWidth', 1.5, 'DisplayName', 'CLKH');
    hold off;
    xlabel('Time (μs)');
    ylabel('Clock Level');
    title('Non-overlapping Clock Signals');
    legend('show', 'Location', 'best');
    grid on;
    
    % 简化分析结果
    subplot(2, 3, 5);
    axis off;
    if step3_success && exist('comparison_data', 'var')
        analysis_text = {
            'Simplified Comparison Analysis:',
            '',
            'Data Statistics:',
            sprintf('Total Samples: %d', length(t_sampled)),
            sprintf('Ideal ADC Valid: %d', sum(abs(comparison_data.ideal_output) > 0.001)),
            sprintf('Pipeline ADC Valid: %d', sum(abs(comparison_data.pipeline_output) > 0.001)),
            sprintf('Sampling Points: %d', length(comparison_data.ideal_sampling_indices)),
            '',
            'Time Information:',
            sprintf('Time Range: %.3f-%.3f μs', min(t_sampled)*1e6, max(t_sampled)*1e6),
            sprintf('Time Step: 10 ps (fixed)'),
            sprintf('Sampling Frequency: %.1f MHz', params.fs/1e6),
            '',
            'Model Versions:',
            'Pipeline ADC: v4 (Event-driven)',
            'Ideal ADC: v2 (CLKH-based)',
            'Comparison: v5 (Simplified)'
        };
    else
        analysis_text = {
            'Analysis Information:',
            'Comparison data unavailable',
            '',
            'Expected Behavior:',
            '• Direct waveform comparison',
            '• No alignment required',
            '• Visual inspection focus',
            '• Statistical overview'
        };
    end
    
    text(0.05, 0.95, analysis_text, 'FontSize', 9, 'VerticalAlignment', 'top', ...
         'FontName', 'Consolas');
    
    % 采样时间分布对比
    subplot(2, 3, 6);
    if step3_success && exist('comparison_data', 'var')
        pipeline_valid_indices = find(abs(comparison_data.pipeline_output) > 0.001);
        ideal_valid_indices = find(abs(comparison_data.ideal_output) > 0.001);
        
        if ~isempty(pipeline_valid_indices) && ~isempty(ideal_valid_indices)
            % 显示前500个有效点的时间分布
            display_count = min(500, min(length(pipeline_valid_indices), length(ideal_valid_indices)));
            
            plot(t_sampled(pipeline_valid_indices(1:display_count))*1e6, ...
                 ones(display_count, 1), 'r.', 'MarkerSize', 8, 'DisplayName', 'Pipeline ADC');
            hold on;
            plot(t_sampled(ideal_valid_indices(1:display_count))*1e6, ...
                 2*ones(display_count, 1), 'g.', 'MarkerSize', 8, 'DisplayName', 'Ideal ADC');
            hold off;
            
            ylim([0.5, 2.5]);
            xlabel('Time (μs)');
            ylabel('ADC Type');
            title('Sampling Time Distribution');
            legend('show', 'Location', 'best');
            grid on;
            set(gca, 'YTick', [1, 2], 'YTickLabel', {'Pipeline', 'Ideal'});
        end
    end
    
    fprintf('✓ 简化的理想ADC vs 流水线ADC对比图生成完成\n');
    
    % 可视化2: v4版本流水线ADC各级输出波形图
    if step2_success
        figure('Name', 'v4 Pipeline ADC Stage-wise Output Analysis', 'Position', [150, 150, 1400, 1000]);
        
        % SHA级输出
        subplot(3, 4, 1);
        sha_indices = find(abs(stage_history.sha) > 0.001);
        if ~isempty(sha_indices)
            sha_samples = min(1000, length(sha_indices));
            stairs(t_sampled(sha_indices(1:sha_samples))*1e6, ...
                   stage_history.sha(sha_indices(1:sha_samples)), 'g-', 'LineWidth', 1.5);
        end
        xlabel('Time (us)');
        ylabel('Amplitude (V)');
        title('v4-SHA Stage Output');
        grid on;
        
        % STAGE1-STAGE8各级模拟输出
        for stage = 1:min(8, params.num_stages)
            subplot(3, 4, stage+1);
            analog_field = ['stage_', num2str(stage), '_analog'];
            if isfield(stage_history, analog_field)
                stage_indices = find(abs(stage_history.(analog_field)) > 0.001);
                if ~isempty(stage_indices)
                    stage_samples = min(1000, length(stage_indices));
                    stairs(t_sampled(stage_indices(1:stage_samples))*1e6, ...
                           stage_history.(analog_field)(stage_indices(1:stage_samples)), ...
                           '-', 'LineWidth', 1.5);
                end
            end
            xlabel('Time (us)');
            ylabel('Amplitude (V)');
            title(sprintf('v4-STAGE%d Output', stage));
            grid on;
        end
        
        % v4最终DAC输出
        subplot(3, 4, 10);
        final_indices = find(abs(final_dac_output) > 0.001);
        if ~isempty(final_indices)
            final_samples = min(1000, length(final_indices));
            stairs(t_sampled(final_indices(1:final_samples))*1e6, ...
                   final_dac_output(final_indices(1:final_samples)), 'r-', 'LineWidth', 1.5);
        end
        xlabel('Time (us)');
        ylabel('Amplitude (V)');
        title('v4 Final DAC Output');
        grid on;
        
        % v4统计信息
        subplot(3, 4, [11, 12]);
        axis off;
        
        % 计算v4版本各级有效样本数
        sha_valid = sum(abs(stage_history.sha) > 0.001);
        stage_valid = zeros(params.num_stages, 1);
        for stage = 1:params.num_stages
            analog_field = ['stage_', num2str(stage), '_analog'];
            if isfield(stage_history, analog_field)
                stage_valid(stage) = sum(abs(stage_history.(analog_field)) > 0.001);
            end
        end
        final_valid = sum(abs(final_dac_output) > 0.001);
        binary_valid = sum(any(binary_output ~= 0, 2));
        
        stats_text = {
            'v4 Valid Sample Statistics by Stage:',
            sprintf('SHA Stage: %d (%.1f%%)', sha_valid, 100*sha_valid/length(t_sampled)),
            sprintf('STAGE1-4: [%d,%d,%d,%d]', stage_valid(1:4)),
            sprintf('STAGE5-8: [%d,%d,%d,%d]', stage_valid(5:8)),
            sprintf('Final DAC: %d (%.1f%%)', final_valid, 100*final_valid/length(t_sampled)),
            sprintf('10-bit Digital: %d (%.1f%%)', binary_valid, 100*binary_valid/length(t_sampled)),
            '',
            'v4 Processing Information:',
            sprintf('Processed Events: %d', timing_info.processed_events),
            sprintf('Valid Output Events: %d', timing_info.valid_outputs),
            sprintf('Processing Efficiency: %.1f%%', 100*timing_info.processing_efficiency),
            sprintf('Data Alignment Quality: %.3f', timing_info.data_alignment_quality),
            '',
            'v4 Diagnostics:',
            sprintf('Output Validity: %.1f%% (Target>5%%)', 100*final_valid/length(t_sampled)),
            'v4 Features: Simplified processing, direct transfer',
            'Recommendation: Continue optimization to improve valid output rate'
        };
        text(0.05, 0.95, stats_text, 'FontSize', 9, 'VerticalAlignment', 'top', ...
             'FontName', 'Consolas');
        
        fprintf('✓ v4版本流水线ADC各级波形图生成完成\n');
    else
        fprintf('! 跳过v4版本流水线ADC波形图（主处理失败）\n');
    end
    
catch vis_error
    fprintf('! v5版本可视化生成失败: %s\n', vis_error.message);
end

%% 步骤4: v5版本综合结果评估
fprintf('\n[步骤4/4] v5版本综合结果评估...\n');

test_results = [step1_success, step2_success, step3_success];
test_names = {
    '高精度自适应时间基准生成',
    'v4版本事件处理核心流水线ADC',
    '简化理想ADC对比 (v5版本)'
};

passed_tests = sum(test_results);
total_tests = length(test_results);

fprintf('\n%s\n', repmat('=', 1, 60));
fprintf('流水线ADC模型功能验证结果汇总 (v5版本)\n');
fprintf('%s\n', repmat('=', 1, 60));

for i = 1:length(test_results)
    status = {'✗ 失败', '✓ 通过'};
    fprintf('%s: %s\n', test_names{i}, status{test_results(i) + 1});
end

fprintf('\n总体完成度: %d/%d (%.1f%%)\n', passed_tests, total_tests, ...
        100*passed_tests/total_tests);

fprintf('\n=== 流水线ADC模型完整功能验证完成 (v5版本) ===\n');
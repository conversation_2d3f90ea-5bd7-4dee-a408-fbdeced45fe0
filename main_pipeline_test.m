%% 流水线ADC模型完整功能验证主脚本
% 验证模型是否完全符合用户设计思路与要求
% 重点：理想状态、两种模拟输出、精确时序控制
% VERSION HISTORY:
% v1: 原始版本 - 基础功能验证
% v2: 增加v2版本事件驱动核心支持
% v3: 集成v3版本事件处理核心，优化理想ADC对比
% v4: 完全重构版本 - 回归实际电路设计思路
% v5: 简化波形对比功能，移除复杂对齐和偏差计算逻辑
% v6: 适配重构后的event_driven_pipeline_adc_v4新接口和结构体输出格式
% v6.1: 增强可视化样本量 - 使用均匀抽样策略提供更充分的数据展示

clear; clc; close all;

fprintf('=== 流水线ADC模型完整功能验证 ===\n');

%% 设置测试参数
params = struct();
params.Vref = 1.0;                % 参考电压
params.num_stages = 8;            % 8级流水线
params.fs = 100e6;               % 100MHz采样频率
params.resolution = 10;           % 10位分辨率
params.Vcm = 0;                   % 共模电压
params.f_in = 23/1024*100e6;            % 输入正弦波频率 (符合相干采样要求)
params.num_sine_cycles = 30;       % 30个正弦波周期
params.num_cycles = 30;             % 与num_sine_cycles一致

fprintf('ADC参数设置完成:\n');
fprintf('  流水线级数: %d级\n', params.num_stages);
fprintf('  采样频率: %.1f MHz\n', params.fs/1e6);
fprintf('  输入频率: %.3f MHz\n', params.f_in/1e6);
fprintf('  分辨率: %d bit\n', params.resolution);
fprintf('  总周期数: %d个\n', params.num_cycles);

%% 设置误差参数矩阵（零误差理想验证）
% 新格式：8×3矩阵 [gain_error, cap_mismatch, offset_error]
% 8行对应8级流水线，3列对应3种误差参数
rel_error_params = zeros(params.num_stages, 3);

% 非零误差参数设置示例（注释掉，用于参考）：
% rel_error_params = [
%     % Stage1: [gain_error, cap_mismatch, offset_error]
%     0.01,  0.005,  0.001;  % 1%增益误差, 0.5%电容失配, 1mV偏移
%     0.01,  0.005,  0.001;  % Stage2
%     0.01,  0.005,  0.001;  % Stage3
%     0.01,  0.005,  0.001;  % Stage4
%     0.01,  0.005,  0.001;  % Stage5
%     0.01,  0.005,  0.001;  % Stage6
%     0.01,  0.005,  0.001;  % Stage7
%     0.01,  0.005,  0.001   % Stage8
% ];

fprintf('\n误差参数矩阵设置完成:\n');
fprintf('  矩阵尺寸: %d×%d (8级流水线×3种误差)\n', size(rel_error_params));
fprintf('  误差类型: [增益误差, 电容失配误差, 偏移误差]\n');
fprintf('  误差值: 全零 (理想验证模式)\n');
fprintf('  备注: 如需测试非理想情况，请取消注释非零误差参数设置示例\n');

%% 初始化变量
t_sampled = [];
sine_wave = [];
clks = [];
clkh = [];

%% 步骤1: 生成时间基准和信号（用于理想ADC对比）
fprintf('\n[步骤1/4] 生成时间基准和信号（用于理想ADC对比）...\n');

try
    % 生成时间基准、时钟信号和输入信号
    [t_sampled, sine_wave, clks, clkh, ~, ~] = generate_timebase(params);
    
    % 检查是否成功生成信号
    if isempty(t_sampled) || isempty(sine_wave) || isempty(clks) || isempty(clkh)
        error('信号生成失败：未能生成有效的输入波形或时钟信号');
    end
    
    fprintf('✓ 时间基准和信号生成成功\n');
    fprintf('  总样本数: %d\n', length(t_sampled));
    fprintf('  时间范围: [%.6f, %.6f]s\n', min(t_sampled), max(t_sampled));
    fprintf('  时间步长: 10 ps (固定)\n');
    
    % 验证非交叠时钟
    overlap_check = any((clks == 1) & (clkh == 1));
    if ~overlap_check
        fprintf('  非交叠时钟验证: ✓ 通过\n');
    else
        fprintf('  非交叠时钟验证: ✗ 检测到重叠\n');
    end
    
    step1_success = true;
    
catch ME
    fprintf('✗ 时间基准和信号生成失败: %s\n', ME.message);
    step1_success = false;
end

%% 步骤2: 验证v6适配版事件驱动流水线ADC处理
if step1_success
    fprintf('\n[步骤2/4] 验证v6适配版事件驱动流水线ADC处理...\n');
    
    try
        % 创建差分输入信号
        Vin_p = sine_wave*1/2;
        Vin_n = -sine_wave*1/2;  % 单端转差分
        
        % 使用新接口调用event_driven_pipeline_adc_v4（v6适配版）
        fprintf('使用v6适配版流水线ADC模型(新接口+结构体输出)...\n');
        fprintf('误差参数: 8×3零误差矩阵 (理想验证模式)\n');
        [correction_output_struct, processing_info, correction_metrics, raw_analog_data] = ...
            event_driven_pipeline_adc_v4(Vin_p, Vin_n, params, rel_error_params);
        
        % 从结构体中提取数据
        if ~isempty(correction_output_struct.signals)
            % 提取最终模拟输出（第1个信号）
            final_analog_output = correction_output_struct.signals(1).values;
            
            % 提取10位数字输出（第2-11个信号）
            binary_output = zeros(length(final_analog_output), 10);
            for bit_idx = 1:10
                binary_output(:, bit_idx) = correction_output_struct.signals(bit_idx + 1).values;
            end
            
            % 验证输出有效性
            final_valid = sum(abs(final_analog_output) > 0.001);
            binary_valid = sum(any(binary_output ~= 0, 2));
            
            fprintf('✓ v6适配版事件驱动流水线ADC处理完成\n');
            fprintf('  最终模拟输出有效样本: %d/%d (%.1f%%)\n', ...
                    final_valid, length(final_analog_output), 100*final_valid/length(final_analog_output));
            fprintf('  10位数字输出有效样本: %d/%d (%.1f%%)\n', ...
                    binary_valid, size(binary_output,1), 100*binary_valid/size(binary_output,1));
            
            % 处理统计信息
            fprintf('  处理事件数: %d\n', processing_info.processed_samples);
            fprintf('  数字输出事件: %d\n', processing_info.digital_outputs);
            fprintf('  数字校正效率: %.1f%%\n', 100*correction_metrics.processing_efficiency);
            
            step2_success = (final_valid > 0);
            
        else
            error('校正输出结构体为空');
        end
        
    catch ME
        fprintf('✗ v6适配版事件处理核心处理失败: %s\n', ME.message);
        step2_success = false;
    end
else
    step2_success = false;
    fprintf('\n[步骤2/4] 跳过v6适配版ADC处理测试\n');
end

%% 步骤3: 验证理想ADC对比 (v6适配版本)
if step1_success
    fprintf('\n[步骤3/4] 验证理想ADC对比 (v6适配版本)...\n');
    
    try
        % 生成理想ADC参考 - 使用CLKH高电平作为理想ADC采样时刻
        ideal_sampling_indices = find(clkh == 1);
        [ideal_output, ~, ~, ~] = run_ideal_adc_v2(0, params, t_sampled, clkh);
        
        ideal_valid = sum(abs(ideal_output) > 0.001);
        fprintf('✓ 理想ADC参考生成成功 (基于CLKH上升沿)\n');
        fprintf('  理想ADC有效输出: %d\n', ideal_valid);
        fprintf('  采样时刻数: %d\n', length(ideal_sampling_indices));
        
        if step2_success && ideal_valid > 0
            % 准备对比数据
            fprintf('启动v6适配版波形对比分析...\n');
            
            % 存储对比数据用于可视化
            comparison_data = struct();
            comparison_data.pipeline_output = final_analog_output;
            comparison_data.ideal_output = ideal_output;
            comparison_data.ideal_sampling_indices = ideal_sampling_indices;
            comparison_data.binary_output = binary_output;
            comparison_data.raw_analog_data = raw_analog_data;
            
            fprintf('✓ v6适配版波形对比分析完成\n');
            fprintf('  流水线ADC有效输出: %d\n', sum(abs(final_analog_output) > 0.001));
            fprintf('  理想ADC有效输出: %d\n', ideal_valid);
            fprintf('  数据已准备用于新的图形化对比\n');
            
        else
            fprintf('! 无法进行对比：流水线ADC或理想ADC输出无效\n');
        end
        
        step3_success = true;
        
    catch ME
        fprintf('✗ 理想ADC对比失败: %s\n', ME.message);
        step3_success = false;
    end
else
    step3_success = false;
    fprintf('\n[步骤3/4] 跳过理想ADC对比\n');
end

%% v6.1版本增强可视化功能
fprintf('\n[可视化] 生成v6.1版本增强样本量的可视化图形...\n');

try
    %% 图1: 流水线ADC vs 理想ADC输出对比（2个子图）
    if step3_success && exist('comparison_data', 'var') && ~isempty(comparison_data)
        figure('Name', 'Pipeline ADC vs Ideal ADC Output Comparison', 'Position', [100, 100, 1400, 600]);
        
        % 获取2us时间范围内的数据索引
        time_limit_indices = find(t_sampled <= 2e-6);
        
        % 子图1: 流水线ADC最终模拟输出
        subplot(1, 2, 1);
        pipeline_valid_idx = find(abs(comparison_data.pipeline_output) > 0.001);
        % 限制在2us时间范围内的有效数据
        pipeline_display_idx = intersect(pipeline_valid_idx, time_limit_indices);
        if ~isempty(pipeline_display_idx)
            plot(t_sampled(pipeline_display_idx)*1e6, ...
                 comparison_data.pipeline_output(pipeline_display_idx), ...
                 'r.-', 'MarkerSize', 2, 'LineWidth', 1.5);
        end
        xlabel('Time (μs)');
        ylabel('Amplitude (V)');
        title('Pipeline ADC Final Analog Output');
        grid on;
        
        % 子图2: 理想ADC输出
        subplot(1, 2, 2);
        ideal_valid_idx = find(abs(comparison_data.ideal_output) > 0.001);
        % 限制在2us时间范围内的有效数据
        ideal_display_idx = intersect(ideal_valid_idx, time_limit_indices);
        if ~isempty(ideal_display_idx)
            plot(t_sampled(ideal_display_idx)*1e6, ...
                 comparison_data.ideal_output(ideal_display_idx), ...
                 'g.-', 'MarkerSize', 2, 'LineWidth', 2);
        end
        xlabel('Time (μs)');
        ylabel('Amplitude (V)');
        title('Ideal ADC Output');
        grid on;
        
        fprintf('✓ 图1: 流水线ADC vs 理想ADC输出对比图生成完成\n');
    else
        fprintf('! 跳过图1: 对比数据不可用\n');
    end
    
    %% 图2: 流水线级原始模拟数据可视化（单一坐标系）
    if step2_success && exist('raw_analog_data', 'var') && ~isempty(raw_analog_data)
        figure('Name', 'Pipeline Stage Raw Analog Data Visualization', 'Position', [150, 150, 1400, 800]);
        
        % 获取2us时间范围内的数据索引
        time_limit_indices = find(t_sampled <= 2e-6);
        
        hold on;
        colors = lines(9);  % 9种不同颜色
        stage_labels = {'SHA', 'Stage1', 'Stage2', 'Stage3', 'Stage4', 'Stage5', 'Stage6', 'Stage7', 'Stage8'};
        
        for stage_idx = 1:min(9, length(raw_analog_data.signals))
            stage_data = raw_analog_data.signals(stage_idx).values;
            stage_valid_idx = find(abs(stage_data) > 0.001);
            
            % 限制在2微秒时间范围内的有效数据
            stage_display_idx = intersect(stage_valid_idx, time_limit_indices);
            
            if ~isempty(stage_display_idx)
                plot(t_sampled(stage_display_idx)*1e6, ...
                     stage_data(stage_display_idx), ...
                     'Color', colors(stage_idx, :), 'LineWidth', 1.5, ...
                     'DisplayName', stage_labels{stage_idx});
            end
        end
        hold off;
        
        xlabel('Time (μs)');
        ylabel('Amplitude (V)');
        title('Pipeline Stage Raw Analog Data (SHA + 8 Stages)');
        legend('show', 'Location', 'best');
        grid on;
        
        fprintf('✓ 图2: 流水线级原始模拟数据可视化图生成完成\n');
    else
        fprintf('! 跳过图2: 原始模拟数据不可用\n');
    end
    
    %% 图3: 最终输出与输入信号对比 + 时钟可视化（2个子图）
    if step1_success && step2_success
        figure('Name', 'Input vs Output Signal and Clock Visualization', 'Position', [200, 200, 1400, 800]);
        
        % 获取2us时间范围内的数据索引
        time_limit_indices = find(t_sampled <= 2e-6);
        
        % 子图1: 输入输出信号对比
        subplot(2, 1, 1);
        
        % 显示输入正弦信号（2us时间范围内）
        plot(t_sampled(time_limit_indices)*1e6, sine_wave(time_limit_indices), ...
             'b-', 'LineWidth', 2, 'DisplayName', 'Input Sine Wave');
        hold on;
        
        % 叠加显示流水线ADC最终输出
        if exist('final_analog_output', 'var')
            final_valid_idx = find(abs(final_analog_output) > 0.001);
            % 限制在2微秒时间范围内的有效数据
            final_display_idx = intersect(final_valid_idx, time_limit_indices);
            
            if ~isempty(final_display_idx)
                plot(t_sampled(final_display_idx)*1e6, ...
                     final_analog_output(final_display_idx), ...
                     'r.-', 'MarkerSize', 2, 'LineWidth', 1, 'DisplayName', 'Pipeline ADC Output');
            end
        end
        hold off;
        
        xlabel('Time (μs)');
        ylabel('Amplitude (V)');
        title('Input Signal vs Pipeline ADC Output');
        legend('show', 'Location', 'best');
        grid on;
        
        % 子图2: 非交叠时钟可视化
        subplot(2, 1, 2);
        
        % 显示时钟信号（2us时间范围内）
        plot(t_sampled(time_limit_indices)*1e6, clks(time_limit_indices), ...
             'r-', 'LineWidth', 2, 'DisplayName', 'CLKS');
        hold on;
        plot(t_sampled(time_limit_indices)*1e6, clkh(time_limit_indices) + 1.2, ...
             'b-', 'LineWidth', 2, 'DisplayName', 'CLKH');
        hold off;
        
        xlabel('Time (μs)');
        ylabel('Clock Level');
        title('Non-overlapping Clock Signals');
        legend('show', 'Location', 'best');
        grid on;
        ylim([-0.2, 2.4]);
        
        fprintf('✓ 图3: 输入输出信号对比 + 时钟可视化图生成完成\n');
    else
        fprintf('! 跳过图3: 输入信号或输出数据不可用\n');
    end
    
catch vis_error
    fprintf('! v6版本可视化生成失败: %s\n', vis_error.message);
    fprintf('错误详情: %s\n', getReport(vis_error));
end

%% 步骤4: v6版本综合结果评估
fprintf('\n[步骤4/4] v6版本综合结果评估...\n');

test_results = [step1_success, step2_success, step3_success];
test_names = {
    '时间基准和信号生成';
    'v6适配版事件驱动流水线ADC';
    '理想ADC对比 (v6适配版本)';
};

passed_tests = sum(test_results);
total_tests = length(test_results);

fprintf('\n%s\n', repmat('=', 1, 60));
fprintf('流水线ADC模型功能验证结果汇总 (v6.1增强可视化版本)\n');
fprintf('%s\n', repmat('=', 1, 60));

for i = 1:length(test_results)
    status = {'✗ 失败', '✓ 通过'};
    fprintf('%s: %s\n', test_names{i}, status{test_results(i) + 1});
end

fprintf('\n总体完成度: %d/%d (%.1f%%)\n', passed_tests, total_tests, ...
        100*passed_tests/total_tests);

% 输出详细统计信息
if step2_success
    fprintf('\n=== 详细性能统计 ===\n');
    fprintf('流水线ADC处理统计:\n');
    fprintf('  处理样本数: %d\n', processing_info.processed_samples);
    fprintf('  数字输出事件: %d\n', processing_info.digital_outputs);
    fprintf('  模拟输出事件: %d\n', processing_info.analog_outputs);
    if isfield(processing_info, 'sampling_events')
        fprintf('  CLKS事件数: %d\n', processing_info.sampling_events);
        fprintf('  CLKH事件数: %d\n', processing_info.holding_events);
        fprintf('  空闲事件数: %d\n', processing_info.idle_events);
    end
    
    fprintf('\n数字校正统计:\n');
    fprintf('  校正输出结构体信号数: %d\n', correction_metrics.output_struct_signals);
    fprintf('  校正效率: %.1f%%\n', 100*correction_metrics.processing_efficiency);
    if isfield(correction_metrics, 'analog_output_range')
        fprintf('  模拟输出范围: [%.6f, %.6f] V\n', ...
                correction_metrics.analog_output_range(1), correction_metrics.analog_output_range(2));
    end
end

function [adc_output, binary_output, stage_history, timing_info] = ...
    event_processing_core_v5(Vin_p, Vin_n, t_sampled, clks, clkh, ...
                            timing_control, rel_error_params, params)
%EVENT_PROCESSING_CORE_V5 事件驱动处理核心 (v5版本) - 优化重构版本
%   VERSION HISTORY:
%   v1-v4: 基础功能开发和时序死锁修复
%   v5: 按照改进规范完全重构：
%       1. 周期级标签分配系统
%       2. 三状态流水线状态机 (sampling/holding/idle)
%       3. 原版时钟信号处理 (0/1电平判断)
%       4. 级间握手协议 (valid信号)
%       5. 状态处理函数封装
%
%   改进重点:
%   - 非交叠时钟控制架构
%   - SHA、STAGE2、4、6、8：CLKH采样，CLKS保持
%   - STAGE1、3、5、7、Flash：CLKS采样，CLKH保持
%   - 严格流水线处理顺序
%   - 周期级标签分配和传递

    fprintf('=== 事件驱动处理核心 v5.0 (重构版本) ===\n');
    
    num_samples = length(t_sampled);
    num_stages = params.num_stages;
    fs = params.fs;
    
    % 初始化输出结构
    adc_output = zeros(num_samples, 1);
    binary_output = zeros(num_samples, 10);
    stage_history = initialize_stage_history_unified(num_samples, num_stages);
    timing_info = struct('processed_events', 0, 'valid_outputs', 0, ...
                        'sha_updates', 0, 'pipeline_updates', 0, 'flash_updates', 0, ...
                        'data_propagations', 0, 'cycle_count', 0, 'version', 'v5.0_restructured', ...
                        'processing_efficiency', 0, 'correction_rate', 0, 'data_alignment_quality', 0);
    
    % 计算差分输入
    Vin_diff = (Vin_p - Vin_n) / 2;
    
    % 初始化三状态流水线系统
    pipeline_system = initialize_three_state_pipeline_system(num_stages, params);
    
    % 初始化周期级标签管理器
    tag_manager = initialize_cycle_tag_manager(fs);
    
    fprintf('初始化完成: %d级流水线, %d个样本\n', num_stages, num_samples);
    
    %% 主要处理循环
    for time_idx = 1:num_samples
        current_time = t_sampled(time_idx);
        current_input = Vin_diff(time_idx);
        current_clks = clks(time_idx);
        current_clkh = clkh(time_idx);
        
        % 更新周期级标签管理器
        tag_manager = update_cycle_tag_manager(tag_manager, current_time, fs);
        
        % 处理SHA模块 (CLKH采样，CLKS保持)
        [pipeline_system.sha, stage_history, timing_info] = ...
            process_sha_stage(pipeline_system.sha, current_input, current_clks, current_clkh, ...
                             time_idx, current_time, tag_manager, stage_history, timing_info);
        
        % 处理所有流水线级
        for stage = 1:num_stages
            [pipeline_system.stages{stage}, stage_history, timing_info] = ...
                process_pipeline_stage(pipeline_system.stages{stage}, stage, ...
                                      pipeline_system, current_clks, current_clkh, ...
                                      time_idx, current_time, rel_error_params, params, ...
                                      stage_history, timing_info);
        end
        
        % 处理Flash ADC (CLKS采样，CLKH保持)
        [pipeline_system.flash, stage_history, timing_info] = ...
            process_flash_adc_stage(pipeline_system.flash, pipeline_system.stages{end}, ...
                                   current_clks, current_clkh, time_idx, current_time, ...
                                   params, stage_history, timing_info);
        
        % 记录处理事件
        if current_clks == 1 || current_clkh == 1
            timing_info.processed_events = timing_info.processed_events + 1;
        end
        
        % 进度显示（每10000个样本）
        if mod(time_idx, 10000) == 0
            fprintf('进度: %d/%d样本\n', time_idx, num_samples);
        end
    end
    
    %% 数字校正处理
    fprintf('执行数字校正...\n');
    try
        simplified_timing_control = struct();
        simplified_timing_control.fs = params.fs;
        simplified_timing_control.version = 'v5_optimized_restructured';
        
        [corrected_output, corrected_binary, correction_metrics] = ...
            digital_correction_v3(stage_history, simplified_timing_control, params);
        
        % 合并校正结果
        valid_indices = find(abs(corrected_output) > 0.001);
        for i = 1:length(valid_indices)
            idx = valid_indices(i);
            if idx <= length(adc_output)
                adc_output(idx) = corrected_output(idx);
                binary_output(idx, :) = corrected_binary(idx, :);
            end
        end
        
        timing_info.correction_metrics = correction_metrics;
        
    catch ME
        warning('EVENT_PROCESSING_CORE_V5:CorrectionError', ...
                '数字校正失败: %s', ME.message);
        timing_info.correction_metrics = struct('error', ME.message);
    end
    
    % 统计最终结果
    timing_info.valid_outputs = sum(abs(adc_output) > 0.001);
    timing_info.correction_rate = timing_info.valid_outputs / num_samples;
    timing_info.processing_efficiency = timing_info.valid_outputs / max(1, timing_info.processed_events);
    timing_info.cycle_count = tag_manager.current_cycle;
    
    % 修复：添加缺失的data_alignment_quality字段
    timing_info.data_alignment_quality = timing_info.correction_rate;  % 使用校正率作为对齐质量指标
    
    % 确保历史记录兼容性
    stage_history = update_stage_history_compatibility(stage_history);
    
    % 打印处理摘要
    fprintf('事件驱动核心v5处理完成: 有效输出=%d/%d (%.1f%%)\n', ...
            timing_info.valid_outputs, num_samples, timing_info.correction_rate*100);
end

function pipeline_system = initialize_three_state_pipeline_system(num_stages, params)
%INITIALIZE_THREE_STATE_PIPELINE_SYSTEM 初始化三状态流水线系统

    pipeline_system = struct();
    
    % SHA状态初始化
    pipeline_system.sha = struct(...
        'state', 'idle', ...
        'input_data', 0, ...
        'latched_data', 0, ...
        'output_data', 0, ...
        'data_tag', 0, ...
        'valid_input', false, ...
        'valid_output', false, ...
        'clock_config', struct('sample_clock', 'clkh', 'hold_clock', 'clks'), ...
        'last_update_time', 0);
    
    % 流水线级状态初始化
    pipeline_system.stages = cell(num_stages, 1);
    for stage = 1:num_stages
        % 奇数级: CLKS采样，CLKH保持; 偶数级: CLKH采样，CLKS保持
        if mod(stage, 2) == 1
            sample_clock = 'clks';
            hold_clock = 'clkh';
        else
            sample_clock = 'clkh';
            hold_clock = 'clks';
        end
        
        pipeline_system.stages{stage} = struct(...
            'stage_id', stage, ...
            'state', 'idle', ...
            'input_data', 0, ...
            'latched_data', 0, ...
            'analog_output', 0, ...
            'digital_output', zeros(1, 2), ...
            'data_tag', 0, ...
            'valid_input', false, ...
            'valid_output', false, ...
            'clock_config', struct('sample_clock', sample_clock, 'hold_clock', hold_clock), ...
            'last_update_time', 0);
    end
    
    % Flash ADC状态初始化
    pipeline_system.flash = struct(...
        'state', 'idle', ...
        'input_data', 0, ...
        'latched_data', 0, ...
        'digital_output', zeros(1, 2), ...
        'data_tag', 0, ...
        'valid_input', false, ...
        'valid_output', false, ...
        'clock_config', struct('sample_clock', 'clks', 'hold_clock', 'clkh'), ...
        'delay_counter', 0, ...
        'last_update_time', 0);
end

function tag_manager = initialize_cycle_tag_manager(fs)
%INITIALIZE_CYCLE_TAG_MANAGER 初始化周期级标签管理器

    tag_manager = struct();
    tag_manager.current_cycle = 0;
    tag_manager.current_cycle_tag = 0;
    tag_manager.last_cycle_time = -1;
    tag_manager.cycle_period = 1/fs;
    tag_manager.tag_assignment_log = [];
end

function tag_manager = update_cycle_tag_manager(tag_manager, current_time, fs)
%UPDATE_CYCLE_TAG_MANAGER 更新周期级标签管理器（优化版：改进周期计算）

    % 基于时钟周期的周期计算
    clock_period = 1/fs;
    current_cycle = floor(current_time / clock_period) + 1;
    
    if current_cycle ~= tag_manager.current_cycle
        tag_manager.current_cycle = current_cycle;
        tag_manager.current_cycle_tag = tag_manager.current_cycle_tag + 1;
        tag_manager.last_cycle_time = current_time;
        
        % 限制标签日志大小
        if length(tag_manager.tag_assignment_log) < 1000
            tag_manager.tag_assignment_log(end+1) = tag_manager.current_cycle_tag;
        end
    end
end

function [sha_state, stage_history, timing_info] = process_sha_stage(...
    sha_state, input_data, clks, clkh, time_idx, current_time, ...
    tag_manager, stage_history, timing_info)
%PROCESS_SHA_STAGE 处理SHA级

    new_state = determine_stage_state(clks, clkh, sha_state.clock_config);
    
    % 状态转换处理
    if ~strcmp(sha_state.state, new_state)
        switch new_state
            case 'sampling'
                sha_state = enter_sampling_state(sha_state, input_data, tag_manager.current_cycle_tag);
                
            case 'holding'
                sha_state = enter_holding_state_sha(sha_state);
                
            case 'idle'
                sha_state = enter_idle_state(sha_state);
        end
        
        sha_state.state = new_state;
        sha_state.last_update_time = current_time;
    end
    
    % 记录输出值
    if strcmp(sha_state.state, 'holding')
        stage_history.sha_data(time_idx) = sha_state.output_data;
        stage_history.sha_valid_mask(time_idx) = true;
        timing_info.sha_updates = timing_info.sha_updates + 1;
    else
        stage_history.sha_data(time_idx) = 0;
        stage_history.sha_valid_mask(time_idx) = false;
    end
    
    % 记录标签和时间戳
    stage_history.sha_tags(time_idx) = sha_state.data_tag;
    stage_history.sha_timestamps(time_idx) = current_time;
    
    % 在采样态时持续跟踪输入
    if strcmp(sha_state.state, 'sampling')
        sha_state.input_data = input_data;
    end
end

function [stage_state, stage_history, timing_info] = process_pipeline_stage(...
    stage_state, stage_id, pipeline_system, clks, clkh, time_idx, current_time, ...
    rel_error_params, params, stage_history, timing_info)
%PROCESS_PIPELINE_STAGE 处理流水线级

    new_state = determine_stage_state(clks, clkh, stage_state.clock_config);
    
    % 获取前级状态
    if stage_id == 1
        prev_stage = pipeline_system.sha;
    else
        prev_stage = pipeline_system.stages{stage_id - 1};
    end
    
    % 状态转换处理
    if ~strcmp(stage_state.state, new_state)
        switch new_state
            case 'sampling'
                if prev_stage.valid_output
                    if stage_id == 1
                        prev_output_data = prev_stage.output_data;
                    else
                        prev_output_data = prev_stage.analog_output;
                    end
                    stage_state = enter_sampling_state(stage_state, prev_output_data, prev_stage.data_tag);
                    timing_info.data_propagations = timing_info.data_propagations + 1;
                end
                
            case 'holding'
                stage_state = enter_holding_state_pipeline(stage_state, stage_id, rel_error_params, params);
                
            case 'idle'
                stage_state = enter_idle_state(stage_state);
        end
        
        stage_state.state = new_state;
        stage_state.last_update_time = current_time;
    end
    
    % 定义字段名
    analog_field = sprintf('stage_%d_analog', stage_id);
    digital_field = sprintf('stage_%d_digital', stage_id);
    tag_field = sprintf('stage_%d_tags', stage_id);
    timestamp_field = sprintf('stage_%d_timestamps', stage_id);
    valid_field = sprintf('stage_%d_valid_mask', stage_id);
    
    % 记录输出值
    if strcmp(stage_state.state, 'holding')
        stage_history.(analog_field)(time_idx) = stage_state.analog_output;
        stage_history.(digital_field)(time_idx, :) = stage_state.digital_output;
        stage_history.(valid_field)(time_idx) = true;
        timing_info.pipeline_updates = timing_info.pipeline_updates + 1;
    else
        stage_history.(analog_field)(time_idx) = 0;
        stage_history.(digital_field)(time_idx, :) = [0, 0];
        stage_history.(valid_field)(time_idx) = false;
    end
    
    % 记录标签和时间戳
    stage_history.(tag_field)(time_idx) = stage_state.data_tag;
    stage_history.(timestamp_field)(time_idx) = current_time;
    
    % 在采样态时检查前级数据变化
    if strcmp(stage_state.state, 'sampling') && prev_stage.valid_output
        if stage_id == 1
            stage_state.input_data = prev_stage.output_data;
        else
            stage_state.input_data = prev_stage.analog_output;
        end
        stage_state.data_tag = prev_stage.data_tag;
        stage_state.valid_input = true;
    end
end

function [flash_state, stage_history, timing_info] = process_flash_adc_stage(...
    flash_state, last_pipeline_stage, clks, clkh, time_idx, current_time, ...
    params, stage_history, timing_info)
%PROCESS_FLASH_ADC_STAGE 处理Flash ADC级

    new_state = determine_stage_state(clks, clkh, flash_state.clock_config);
    
    % 状态转换处理
    if ~strcmp(flash_state.state, new_state)
        switch new_state
            case 'sampling'
                if last_pipeline_stage.valid_output
                    flash_state = enter_sampling_state(flash_state, last_pipeline_stage.analog_output, last_pipeline_stage.data_tag);
                    timing_info.data_propagations = timing_info.data_propagations + 1;
                end
                
            case 'holding'
                flash_state = enter_holding_state_flash(flash_state, params);
                
            case 'idle'
                flash_state = enter_idle_state(flash_state);
        end
        
        flash_state.state = new_state;
        flash_state.last_update_time = current_time;
    end
    
    % 记录输出值
    if strcmp(flash_state.state, 'holding')
        stage_history.flash(time_idx, :) = flash_state.digital_output;
        stage_history.flash_valid_mask(time_idx) = true;
        timing_info.flash_updates = timing_info.flash_updates + 1;
    else
        stage_history.flash(time_idx, :) = [0, 0];
        stage_history.flash_valid_mask(time_idx) = false;
    end
    
    % 记录标签和时间戳
    stage_history.flash_tags(time_idx) = flash_state.data_tag;
    stage_history.flash_timestamps(time_idx) = current_time;
    
    % 在采样态时跟踪输入
    if strcmp(flash_state.state, 'sampling') && last_pipeline_stage.valid_output
        flash_state.input_data = last_pipeline_stage.analog_output;
        flash_state.data_tag = last_pipeline_stage.data_tag;
        flash_state.valid_input = true;
    end
end

function new_state = determine_stage_state(clks, clkh, clock_config)
%DETERMINE_STAGE_STATE 根据时钟配置确定当前状态

    sample_clock = clock_config.sample_clock;
    hold_clock = clock_config.hold_clock;
    
    if strcmp(sample_clock, 'clks') && clks == 1
        new_state = 'sampling';
    elseif strcmp(sample_clock, 'clkh') && clkh == 1
        new_state = 'sampling';
    elseif strcmp(hold_clock, 'clks') && clks == 1
        new_state = 'holding';
    elseif strcmp(hold_clock, 'clkh') && clkh == 1
        new_state = 'holding';
    else
        new_state = 'idle';
    end
end

function stage_state = enter_sampling_state(stage_state, input_data, data_tag)
%ENTER_SAMPLING_STATE 进入采样态

    stage_state.input_data = input_data;
    stage_state.data_tag = data_tag;
    stage_state.valid_input = true;
    stage_state.valid_output = false;
end

function stage_state = enter_holding_state_sha(stage_state)
%ENTER_HOLDING_STATE_SHA 进入保持态 (SHA特殊处理)

    stage_state.latched_data = stage_state.input_data;
    stage_state.output_data = stage_state.latched_data;
    stage_state.valid_output = true;
    stage_state.valid_input = false;
end

function stage_state = enter_holding_state_pipeline(stage_state, stage_id, rel_error_params, params)
%ENTER_HOLDING_STATE_PIPELINE 进入保持态 (流水线级处理)

    stage_state.latched_data = stage_state.input_data;
    
    % 执行1.5bit ADC + MDAC处理
    [analog_out, digital_out] = process_1p5bit_pipeline_stage_unified(...
        stage_state.latched_data, stage_id, rel_error_params, params);
    
    stage_state.analog_output = analog_out;
    stage_state.digital_output = digital_out;
    stage_state.valid_output = true;
end

function stage_state = enter_holding_state_flash(stage_state, params)
%ENTER_HOLDING_STATE_FLASH 进入保持态 (Flash ADC处理)

    stage_state.latched_data = stage_state.input_data;
    
    % 执行2bit Flash ADC量化
    flash_digital = process_2bit_flash_adc_unified(stage_state.latched_data, params);
    
    stage_state.digital_output = flash_digital;
    stage_state.valid_output = true;
end

function stage_state = enter_idle_state(stage_state)
%ENTER_IDLE_STATE 进入空白态

    stage_state.valid_output = false;
    stage_state.valid_input = false;
    
    if isfield(stage_state, 'output_data')
        stage_state.output_data = 0;
    end
end

function [analog_out, digital_out] = process_1p5bit_pipeline_stage_unified(...
    input_data, stage_num, rel_error_params, params)
%PROCESS_1P5BIT_PIPELINE_STAGE_UNIFIED 统一的1.5bit流水线级处理

    if nargin < 4
        error('至少需要4个输入参数');
    end
    
    % 默认参数处理
    if isempty(rel_error_params) || size(rel_error_params, 1) < stage_num
        gain_error = 0;
        offset_error = 0;
    else
        gain_error = rel_error_params(stage_num, 1);
        offset_error = rel_error_params(stage_num, 2);
    end
    
    % 1.5bit ADC量化
    Vref = params.Vref;
    threshold_high = Vref/4;
    threshold_low = -Vref/4;
    
    % 量化输出
    if input_data > threshold_high
        digital_code = [1, 1];  % +1
        dac_output = Vref/2;
    elseif input_data < threshold_low
        digital_code = [0, 0];  % -1
        dac_output = -Vref/2;
    else
        digital_code = [1, 0];  % 0
        dac_output = 0;
    end
    
    % MDAC输出 (x2增益 + DAC减法 + 误差)
    gain = 2 * (1 + gain_error);
    analog_residue = gain * (input_data - dac_output) + offset_error;
    
    % 输出限幅
    analog_out = max(-Vref, min(Vref, analog_residue));
    digital_out = digital_code;
end

function flash_digital = process_2bit_flash_adc_unified(input_data, params)
%PROCESS_2BIT_FLASH_ADC_UNIFIED 统一的2bit Flash ADC处理

    Vref = params.Vref;
    thresholds = [-3*Vref/4, -Vref/4, Vref/4, 3*Vref/4];
    
    % 2bit量化
    if input_data > thresholds(4)
        flash_digital = [1, 1];  % 11
    elseif input_data > thresholds(3)
        flash_digital = [1, 0];  % 10
    elseif input_data > thresholds(2)
        flash_digital = [0, 1];  % 01
    else
        flash_digital = [0, 0];  % 00
    end
end

function stage_history = initialize_stage_history_unified(num_samples, num_stages)
%INITIALIZE_STAGE_HISTORY_UNIFIED 统一的级历史记录初始化

    stage_history = struct();
    
    % SHA历史记录
    stage_history.sha_data = zeros(num_samples, 1);
    stage_history.sha_tags = zeros(num_samples, 1);
    stage_history.sha_timestamps = zeros(num_samples, 1);
    stage_history.sha_valid_mask = false(num_samples, 1);
    
    % 流水线级历史记录
    for stage = 1:num_stages
        analog_field = sprintf('stage_%d_analog', stage);
        digital_field = sprintf('stage_%d_digital', stage);
        tag_field = sprintf('stage_%d_tags', stage);
        timestamp_field = sprintf('stage_%d_timestamps', stage);
        valid_field = sprintf('stage_%d_valid_mask', stage);
        
        stage_history.(analog_field) = zeros(num_samples, 1);
        stage_history.(digital_field) = zeros(num_samples, 2);
        stage_history.(tag_field) = zeros(num_samples, 1);
        stage_history.(timestamp_field) = zeros(num_samples, 1);
        stage_history.(valid_field) = false(num_samples, 1);
    end
    
    % Flash ADC历史记录
    stage_history.flash = zeros(num_samples, 2);
    stage_history.flash_tags = zeros(num_samples, 1);
    stage_history.flash_timestamps = zeros(num_samples, 1);
    stage_history.flash_valid_mask = false(num_samples, 1);
    
    % 兼容性字段
    stage_history.sha = stage_history.sha_data;
end

function stage_history = update_stage_history_compatibility(stage_history)
%UPDATE_STAGE_HISTORY_COMPATIBILITY 更新历史记录兼容性字段

    if isfield(stage_history, 'sha_data')
        stage_history.sha = stage_history.sha_data;
    end
end
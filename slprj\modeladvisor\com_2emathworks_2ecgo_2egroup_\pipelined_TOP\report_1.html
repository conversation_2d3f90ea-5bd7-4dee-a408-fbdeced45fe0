<!DOCTYPE html>
<html>
<head>
<meta http-equiv="X-UA-Compatible" content="IE=8" /> 
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<title>Code Generation Advisor Report for 'pipelined_TOP'</title>  
<style type="text/css">
<!--
@media screen {    
    /* Table Formatting */
    .AdvTable th { 
        background:#80a0c1 url(data:image/gif;base64,R0lGODlhAQAaAKIAAHSRr3mXtn+fv2V/mX2cvG2JpVxzi4CgwSH5BAAAAAAALAAAAAABABoAAAMJeLrcLCSAMkwCADs=) repeat-x bottom left; 
    }
}

@media all {
    *{ font-family: sans-serif; }

	H3 {
		font-size: 14pt;
		font-weight: 200;
	}
	H4 {
		font-size: 9pt;
		font-weight: normal;
	}
	H5 {
		font-size: 12pt;
		font-style: italic;
		font-weight: bold;
		color: #333333;
		margin-bottom: 2px;
	}
	a[href] {
		color: #005FCE;
	}
	.subsection {
		padding-left: 30px;
	}
    
    .CheckHeading {
		font-size:1.05em;
		font-weight:bold;
	}

    /* Table Formatting */
    table.AdvTable { 
        border-collapse:collapse; border:1px solid #ececec; border-right:none; border-bottom:none; 
    }

    .AdvTable th { 
        padding-left:5px; 
        padding-right:5px; 
        color:#fff; 
        line-height:120%; 
        background:#80a0c1 url(data:image/gif;base64,R0lGODlhAQAaAKIAAHSRr3mXtn+fv2V/mX2cvG2JpVxzi4CgwSH5BAAAAAAALAAAAAABABoAAAMJeLrcLCSAMkwCADs=) repeat-x bottom left; 
        border-right: 1px solid #ececec; 
        border-bottom: 1px solid #ececec; 
    }

    .AdvTable td { 
        padding-left:5px; 
        padding-right:5px; 
        border-right:1px solid #ececec; 
        border-bottom: 1px solid #ececec; 
    }
    
    .AdvTable th p { 
        margin-bottom:0px; 
    }

    .AdvTable p { 
        margin-bottom:10px; 
    }
    
    .AdvTableNoBorder p { 
        margin-bottom:10px; 
    }

    table+span.SDCollapseControl { 
        font-size:0.8em; 
        font-weight:bold; 
    }

    ul+span.SDCollapseControl { 
        margin-left:25px; 
        font-size:0.8em;
        font-weight:bold; 
    }

    ol+span.SDCollapseControl { 
        margin-left:25px; 
        font-size:0.8em; 
        font-weight:bold; 
    }

    .SystemdefinedCollapse { 
        margin-top:0px;
        margin-bottom:5px; 
    }

    div.AllCollapse p, div.AllCollapse table, div.AllCollapse ol, div.AllCollapse ul { 
        margin-top:0pt;
        margin-bottom:0pt; 
        margin-left:18px;
    }

    div.AllCollapse + div { 
        margin-top:0pt;
        margin-bottom:0pt; 
        margin-left:18px; 
    }

    img.CollapseAllControlImage { 
        float:left; 
    }

    .SubResultsSummary {
        padding-left:30px;
    }

    .EmptyFolderMessage {
        color:gray;
        margin:10px 0 0 30px;
        font-size:0.8em;
    }
	
    .CsEml-Symbol-Status-Pass
    { 
        color: green;
        font-family: monospace;
    }

    .CsEml-Symbol-Status-Warn
        { 
        color: orange;
        font-family: monospace;
    }

    .CsEml-Symbol-Status-Fail
    { 
        color: red;
        font-family: monospace;
    }
    
    .CsEml-Line-Number
    {
        color: #A0A0A0;
        font-style: italic;
        font-family: monospace;
    }

    .CsEml-Code
    {
        color: blue;
        font-family: monospace;
    }

    .CsEml-Code-Fragment
    {
        color: blue;
        font-weight: bold;
        font-family: monospace;
        margin-right: 0;
        padding-right: 0;
        margin-left: 0;
        padding-left: 0;
    }

    .CsEml-Function-Type
    {
        color:       #A0A0A0;
        font-style:  italic;
        font-family: monospace;
    }

}
-->
</style>

<style type="text/css">
/* Copyright 2013-2023 The MathWorks, Inc. */
<!--
@media screen {
    #Container {
        overflow-x:hidden;
    }

	#ControlPanel {
		position: fixed;
		top: 5px;
		left: 10px;
		bottom: 5px;
		width: 210px;
		padding: 2px 0 2px 2px;
		border: 1px solid #EEEEEE;
		font-family: sans-serif;
		font-size: 0.85em;
	}

	#HidePanelControl {
		position:absolute;
		top:0;
		left:204px;
		width:8px;
		height:100%;
		font-size: 8px;
	}
	
	#HidePanelControl:hover {
		background-color: #EFEFFB;
	}
	
	#HidePanelControlInner {
		position:absolute; top:50%;
	}

	#ControlsCheckFiltering {
		position:absolute;
		top:2px;
		border: 1px solid #ececec;
		padding-bottom: 8px;
		margin-top: 0;
		margin-bottom:0;
		height:190px;
		width:200px;
	}

	#TextFilter{
		position:absolute;
		top:202px;
		padding-top:5px;
        margin-left:12px;  
	}
		
	#ControlsTOC {
		position: absolute;
		top:242px;
		bottom:130px;
		margin-top: 0;
		margin-bottom: 0;
		width:200px;
		border: 1px solid #ececec;
	}
	
	#ControlsView {
		position:absolute;
		bottom:2px;
		width:200px;
		margin-top: 0;
		margin-bottom: 0;
		height:110px;
		border: 1px solid #ececec;
	}
	
	#TOCScrollableArea {
		position:absolute;
		top:30px;
		bottom:0;
		width:100%;
		padding-bottom:5px;
		overflow:auto;
	}
	
	#ControlsCheckFiltering input[type=checkbox] { margin-right: 15px; }
	
	#ControlPanel h2 {
		margin: 0 0 10px 0;
		background-color: #ECECEC;
		font-size: 1em;
		font-weight: normal;
		color: #333333;
		padding: 2px 5px 1px 5px;
		height:20px;
	}

	.ControlPanelTextControl a[href] {
		text-decoration: none;
		font-weight: normal;
		color: #F6F6F6;
	}
	
	.ControlPanelTextControl {
		line-height: 160%;
		font-weight: normal;
		color: #005FCE;
		margin: 0 0 0 5px;
		padding-right:15px;
        padding-left:18px;
        text-indent:-18px;
	}
	
	.ControlPanelTextControl:hover {
		background-color: #EFEFFB;
	}
	
	#TxtFilterInput {
		width: 170px;
		color: gray;
	}

	#CPFilteringTable {
        margin-left:5px;
        width:165px;
        border: none;
    }

    #CPFilteringTable td { border: none; }

    #CPToTopImg { position:relative; top:3px; width:18px; height:17px; }
    #CPCollapseImg { position:relative; top:2px; width:16px; height:16px; margin-right:3px; }

	.ReportContent { margin-left: 225px; }
}

@media print {
	#ControlPanel {
		display: none;
	}

    #ShowPanelControl{
		display:none;
    }
    
    .ReportContent { margin-left: 0px; }
}
-->

</style>

<script type="text/javascript"> <!-- /* Copyright 2013-2017 The MathWorks, Inc */
/* define String.trim() method if not defined (used by filterByText() function) */
if(!String.prototype.trim) {
  String.prototype.trim = function () {
    return this.replace(/^\s+|\s+$/g,'');
  };
}

// rtwreport needs it 
function init() {
    var showFailed = document.getElementById("Failed Checkbox");
    var showPassed = document.getElementById("Passed Checkbox");
    var showWarning = document.getElementById("Warning Checkbox");
    var showNotRun = document.getElementById("Not Run Checkbox");
    var showJustified = document.getElementById("Justified Checkbox");
    var showIncomplete = document.getElementById("Incomplete Checkbox");      
    
    urlQueryStringRegexp = RegExp('\\?(.*)').exec(window.location.search);

    if (urlQueryStringRegexp == null) {
        /* refresh check boxes and search input */
        showFailed.checked = true;
        showPassed.checked = true;
        showWarning.checked = true;
        showNotRun.checked = true;
        showJustified.checked = true;
        showIncomplete.checked = true;
    }
    else 
    {
        showFailed.checked = false;
        showPassed.checked = false;
        showWarning.checked = false;
        showNotRun.checked = false;
        
        urlQueryString = urlQueryStringRegexp[1];
        
        var queryArgs = [];
        
        if (urlQueryString.indexOf("&") == -1)
        {
            // Only 1 argument
            queryArgs[0] = urlQueryString;
        }
        else
        {
            queryArgs = urlQueryString.split("&");
        }
        
        for (var idx = 0; idx < queryArgs.length; ++idx)
        {
            // get in
            if (queryArgs[idx].localeCompare("showPassedChecks") == 0) 
            {
                showPassed.checked = true;
            }
            else if (queryArgs[idx].localeCompare("showWarningChecks") == 0) 
            {
                showWarning.checked = true;
            }
            else if (queryArgs[idx].localeCompare("showFailedChecks") == 0)
            {
                showFailed.checked = true;
            }
            else if (queryArgs[idx].localeCompare("showNotRunChecks") == 0)
            {
                showNotRun.checked = true;
            }
        }
            
        // if nothing is selected, select everything
        if (!showFailed.checked && !showPassed.checked && 
            !showWarning.checked && !showNotRun.checked) 
        {
            showFailed.checked = true;
            showPassed.checked = true;
            showWarning.checked = true;
            showNotRun.checked = true;
            showJustified.checked = true;
            showIncomplete.checked = true;
        }
    }
    
    updateVisibleChecks();
}

function markEmptyFolders(){
	var nodeTypes = ["FailedCheck","PassedCheck",  "WarningCheck", "NotRunCheck"];
	var folderArray = document.querySelectorAll("div.FolderContent");
	
	for (var n=0;n<folderArray.length;n++){
		/* get direct check result children and check visibility */
		var childNodes = folderArray[n].childNodes;
		var noneVisible = true;
		var noChecksInFolder = true;
		
		for (var ni=0;ni<childNodes.length;ni++){
			if (childNodes[ni].nodeType == 1 && childNodes[ni].tagName.toLowerCase() == "div"){
				if (childNodes[ni].className == nodeTypes[0] || childNodes[ni].className == nodeTypes[1] || childNodes[ni].className == nodeTypes[2] || childNodes[ni].className == nodeTypes[3]){
					noChecksInFolder = false;
					if (childNodes[ni].style.display != "none"){
						noneVisible = false;
                        break;
					}
				}
			}
		}
		
		/* Only display hidden message if any checks inside the folders and not just other folders */
		if (!noChecksInFolder){
			var hiddenMessage = folderArray[n].querySelector("div.EmptyFolderMessage");
			
			if (hiddenMessage && noneVisible == true){
				hiddenMessage.style.display = "";
			}else if (hiddenMessage && noneVisible == false){
				hiddenMessage.style.display = "none";
			}else{
				/* hidden message not found */
			}
		}
	}

    return;
}

function updateVisibleChecks( /* show all flags */ checkbox ){
	
	var checkboxes = ["Failed Checkbox", "Passed Checkbox", "Warning Checkbox", "Not Run Checkbox", "Justified Checkbox", "Incomplete Checkbox"];
	var nodeTypes = ["Failed Check","Passed Check",  "Warning Check", "Not Run Check", "Justified Check", "Incomplete Check"];
	var textInput = document.getElementById("TxtFilterInput");
	
	
	if (checkbox && textInput && textInput.color=="gray"){
		var checkType = checkbox.id;
		var ind = checkboxes.indexOf(checkType);
		var nodes = document.getElementsByName(nodeTypes[ind]);
		for (i = 0; i < nodes.length;i++){
		    nodes[i].style.display = checkbox.checked ? "" : "none";
		}
	}
	else{ /* Update all checks if called from init or if a previous text filter was applied */
		for (i = 0; i < checkboxes.length; i++){
			
			  var show = document.getElementById(checkboxes[i]).checked ? "" : "none";
			  var nodes = document.getElementsByName(nodeTypes[i]);
			  for(j = 0; j < nodes.length; j++){
				  nodes[j].style.display = show;
			  }
		   
		}
	}

    /* clear text search */
	if (textInput && checkbox){
		textInput.value = "按检查名称搜索";
        textInput.style.color = "gray";
        textInput.blur;
	}

    markEmptyFolders();

    return; 
}

function filterByText(ev){
	// get all the nodes
	var allNodeTypes = ["Failed Check","Passed Check",  "Warning Check", "Not Run Check"];
	var checkboxes = ["Failed Checkbox", "Passed Checkbox", "Warning Checkbox", "Not Run Checkbox"];
	var nodeTypes = [];
	
	// get nodes depending on filter selections
	for (var n=0; n<checkboxes.length; n++){
		var checkbox = document.getElementById(checkboxes[n]);
		if (checkbox && checkbox.checked){
			nodeTypes.push(allNodeTypes[n]);
		}
	}
	
    var searchNodes = [".CheckHeading"];
    var allnodes = [];
	var alltext = [];
	if (!ev) return;
	var target = ev.target ? ev.target : window.event.srcElement;
	var searchString = target.value;
	
	if (!searchString){
        updateVisibleChecks();  // clear all and display by other filters
	}else{
		for (i = 0; i < nodeTypes.length; i++){
			var nodes = document.getElementsByName(nodeTypes[i]);
			for (j = 0; j < nodes.length; j++){
				// get text from check heading
				var checkContent = nodes[j].querySelector(searchNodes).innerHTML;
				// creaet a regular expression to ignore case
				var ss = new RegExp(searchString.trim(), "i");
				if (ss.exec(checkContent)){
				   nodes[j].style.display = "";
				}else{
				   nodes[j].style.display = "none";
				}
			}
		}
        markEmptyFolders();
	}	
}


function MATableShrink(o,tagNameStr){
    var temp = document.getElementsByName(tagNameStr);
    var classUsed = document.getElementsByName('EmbedImages'); 
    var embeddedMode = !(classUsed.length == 0);
    var img = o.querySelector("img");

    if (temp[0].style.display == "") 
    {
        temp[0].style.display = "none";
        if (embeddedMode)
        {
            img.src = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAABGdBTUEAALGOfPtRkwAAACBjSFJNAAB6JQAAgIMAAPn/AACA6QAAdTAAAOpgAAA6mAAAF2+SX8VGAAAAkUlEQVR42mL8//8/AyUAIICYGCgEAAFEsQEAAUSxAQABxIIu0NTURDBQ6urqGGFsgABiwaagpqYOp+aWliYUPkAAEfQCCwt+JQABRHEYAAQQCzE2w9h//vzDUAcQQDgNgCkGacamEQYAAohiLwAEEEED8NkOAgABxEJMVOEDAAHESGlmAgggisMAIIAoNgAgwAC+/BqtC+40NQAAAABJRU5ErkJggg==";
        }
        else
        {
            img.src = "plus.png";
        }
    } 
    else 
    {
        temp[0].style.display = "";
        if (embeddedMode)
        {
            img.src = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAABGdBTUEAALGOfPtRkwAAACBjSFJNAAB6JQAAgIMAAPn/AACA6QAAdTAAAOpgAAA6mAAAF2+SX8VGAAAAhklEQVR42mL8//8/AyUAIICYGCgEAAFEsQEAAUSxAQABxIIu0NTURDBQ6urqGGFsgABiwaagpqYOp+aWliYUPkAAUewFgACi2ACAAGLBKcGCafafP/8wxAACCKcB2BRjAwABRLEXAAKIYgMAAoiFmKjCBwACiJHSzAQQQBR7ASCAKDYAIMAAUtQUow+YsTsAAAAASUVORK5CYII=";
        }
        else
        {
            img.src = "minus.png";
        }
    }
}

// rtwreport needs it 
function updateHyperlink() {
    docObj = window.document;
    loc = document.location;
    var aHash = "";
    var externalweb = "false";
    if (loc.search || loc.hash) {
        if (loc.search)
            aHash = loc.search.substring(1);
        else
            aHash = loc.hash.substring(1);
    }    
    var args = new Array();
    args = aHash.split('&');
    for (var i = 0; i < args.length; ++i) {
        var arg = args[i];
          sep = arg.indexOf('=');
        if (sep != -1) {
            var cmd = arg.substring(0,sep);
            var opt = arg.substring(sep+1);
            switch (cmd.toLowerCase()) {
            case "externalweb":
                externalweb = opt;
                break;
            }
        }
    }        
    if (externalweb === "true") {        
        var objs = docObj.getElementsByTagName("a");
        if (objs.length > 0 && objs[0].removeAttribute) {
            for (var i = 0; i < objs.length; ++i) {           
                if (objs[i].href.indexOf('matlab') > -1)           
                    objs[i].removeAttribute("href");                
            }
        }
    }
    init();
}
    
function findParentNode(e) {
   var parent = e.parentElement;
   if (!!parent) {
    if (parent.id == "") {   
       return findParentNode(parent);
    } else {
       return parent;
    }
   } else {
    return null;
   }
}

function expandParentNode(e) {
   var parent = findParentNode(e);
   while (!!parent) { 
    var prefix = "FolderControl_";
    var folderControl = document.getElementById(prefix.concat(parent.id));
    if (parent.style.display == "none") {       
       MATableShrink(folderControl,parent.id);
    }
    parent = findParentNode(parent);
   }
}

function isHidden(e) {
    return (e.offsetParent === null)
}

function navigateToElement(eleID) {
   var e = document.getElementById(eleID);
   if (isHidden(e)) {
       expandParentNode(e);
   }       
   if (!!e && e.scrollIntoView) {
       e.scrollIntoView();
   }
}

function setTextContent(element, value) {
    var content = element.textContent;
    if (value === undefined) return;
    
    if (content !== undefined) element.textContent = value;
    else element.innerText = value;
}

function hideControlPanel(control){
	var panelComponents = ["ControlsCheckFiltering", "ControlsView", "ControlsTOC"];
	var reportContent = document.querySelector(".ReportContent");
	var controlPanel = document.getElementById("ControlPanel");
	var isHidden = false;
	
    if (reportContent && control && controlPanel) {
    	for (var n=0; n<panelComponents.length; n++){
			var component = document.getElementById(panelComponents[n]);
			
			if (component && component.style.display == ""){
				component.style.display = "none";
            } else if (component && component.style.display == "none"){
				component.style.display = "";
			}
		}
		
		if (controlPanel.style.width != "6px"){
			reportContent.style.marginLeft = "25px";
	    	controlPanel.style.width = "6px";
	    	control.style.left = "0px";
	       	var innerDiv = control.querySelector("#HidePanelControlInner");
	       	setTextContent(innerDiv, "\u25BA");
	    } else {
	    	reportContent.style.marginLeft = "225px";
        	controlPanel.style.width = "210px";
        	control.style.left = "204px";
        	var innerDiv = control.querySelector("#HidePanelControlInner");
        	setTextContent(innerDiv, "\u25C0");
        }
    }
}
/* Copyright 2013 The MathWorks, Inc. */
//COLLAPSIBLE FEATURE

// global variables
var GlobalCollapseState = "off";

function collapseSDHelper(o, CollElementParent, disp, mode){
    var CollElement = CollElementParent; /* ul/ol with lists, tbody with table */

    if (CollElement.nodeName == "UL" || CollElement.nodeName == "OL"){
        var CollName = "LI";
    }else if (CollElement.nodeName == "TABLE"){
        if (CollElement.querySelector('tbody')) {
            /* tr modes are child nodes of tbody node */
            CollElement = CollElement.querySelector('tbody');
        }
        var CollName = "TR";
    }
    
    // get children (li for list and tr for table)
    var children = CollElement.childNodes;

    var nElements = 0;
    for (var i=0;i<children.length;i++){
        if (children[i].nodeName == CollName){
            nElements = nElements + 1;
            if (nElements > 5){
                children[i].style.display = disp;
            }
        }
    }
    if (disp == "none"){
        if (CollName == "LI"){
            var text = " 项目)";
        }else{
            var text = " 行)";
        }
        
        var textNode = document.createTextNode(("\u2228 更多 (" + (nElements-5) + text));
        o.replaceChild(textNode,o.firstChild);

        CollElementParent.setAttribute("dataCollapse", "on");

        /* scroll to element if it is not visible */
        if (mode == "single" && CollElement.offsetTop < document.documentElement.scrollTop){
			CollElement.scrollIntoView();
		}
    }else{
        var textNode = document.createTextNode(("\u2227 " + "更少"));
        o.replaceChild(textNode,o.firstChild);

        CollElementParent.setAttribute("dataCollapse", "off");
    }
}

function collapseSD(o, ID){
    var CollElement = document.getElementById(ID);
    if (CollElement != null){
        if (CollElement.getAttribute("dataCollapse") == "off"){
            var disp="none";
        }else{
            var disp="";
        }
        collapseSDHelper(o, CollElement, disp, "single");
    }
}

function collapseAllHelper(o, CollElement, CollInfo, disp){

    if (CollElement != null){
        var img = o.querySelector("img");
        if (disp == "none"){
            CollElement.style.display = disp;
			img.src = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAABGdBTUEAALGOfPtRkwAAACBjSFJNAAB6JQAAgIMAAPn/AACA6QAAdTAAAOpgAAA6mAAAF2+SX8VGAAAAkUlEQVR42mL8//8/AyUAIICYGCgEAAFEsQEAAUSxAQABxIIu0NTURDBQ6urqGGFsgABiwaagpqYOp+aWliYUPkAAEfQCCwt+JQABRHEYAAQQCzE2w9h//vzDUAcQQDgNgCkGacamEQYAAohiLwAEEEED8NkOAgABxEJMVOEDAAHESGlmAgggisMAIIAoNgAgwAC+/BqtC+40NQAAAABJRU5ErkJggg==";
        }else{
            CollElement.style.display = disp;
			img.src = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAABGdBTUEAALGOfPtRkwAAACBjSFJNAAB6JQAAgIMAAPn/AACA6QAAdTAAAOpgAAA6mAAAF2+SX8VGAAAAhklEQVR42mL8//8/AyUAIICYGCgEAAFEsQEAAUSxAQABxIIu0NTURDBQ6urqGGFsgABiwaagpqYOp+aWliYUPkAAUewFgACi2ACAAGLBKcGCafafP/8wxAACCKcB2BRjAwABRLEXAAKIYgMAAoiFmKjCBwACiJHSzAQQQBR7ASCAKDYAIMAAUtQUow+YsTsAAAAASUVORK5CYII=";
        }

        if (CollInfo != null){
            if (disp == "none"){
                CollInfo.style.display = "";
            }else{
                CollInfo.style.display = "none";
            }
        }
    }
}

function collapseAll(o, ID, ID2){
    var CollElement = document.getElementById(ID);
    var CollInfo = document.getElementById(ID2);

    if (CollElement.style.display == ""){
        var disp = "none";
    }else{
        var disp = "";
    }

    collapseAllHelper(o, CollElement, CollInfo, disp);
}

function expandCollapseAll(o){
	/* IE has no method for getting elements by class name. Use querySelectorAll instead 
       Note: requires IE not to be in IE7 compatability mode */
    var SDCollapse = document.querySelectorAll(".SystemdefinedCollapse");
    var Button = null;

    if (GlobalCollapseState == "off"){
        var disp = "none";
        GlobalCollapseState = "on";

        if (o && o.firstChild.nodeType == 3) {
            var textNode = o.firstChild;
        	textNode.nodeValue = " 显示检查详细信息";
		}
    }else{
        var disp = "";
        GlobalCollapseState = "off";

        if (o && o.firstChild.nodeType == 3) {
            var textNode = o.firstChild;
        	textNode.nodeValue = " 隐藏检查详细信息";
		}
    }

    for (var i=0;i<SDCollapse.length;i++){
        Button = SDCollapse[i].nextSibling;
        while(Button.nodeType !== 1){
            Button = Button.nextSibling;
        }
        //Button = SDCollapse[i].parentNode.querySelector("span.SDCollapseControl");
        collapseSDHelper(Button, SDCollapse[i], disp, "all");
    }
		
    var AllCollapse = document.querySelectorAll(".AllCollapse");

    for (i=0;i<AllCollapse.length;i++){
        // get children of top level collapsible div
        var children = AllCollapse[i].parentNode.children;
        
        // collapsible button is first child
		Button = children[0];
        
        // get content to display in collapsed state
        // this is optional and last child of collapsible div 
        if (Button)
        {
            if (children.length>2)
            {
                collapseAllHelper(Button, AllCollapse[i], children[2], disp);
            }
            else
            {
                collapseAllHelper(Button, AllCollapse[i], null, disp);
            }
        }
    }
}


function expandCollapseAllOnLoad(){
    GlobalCollapseState = "on";
    var Switch = document.getElementById("ExpandCollapseAll");
    expandCollapseAll(Switch);
}
//END COLLAPSIBLE

 --></script></head>  
<body onload="updateHyperlink(); expandCollapseAllOnLoad();">  
<span id="top">

</span>
<!-- mdladv_ignore_start --><div id="Container"><!-- mdladv_ignore_finish -->

<!-- mdladv_ignore_start --><div id="ControlPanel">
<div id="HidePanelControl" title="最小化/最大化控制面板" onclick="hideControlPanel(this)" onmouseover="this.style.cursor='pointer'">
<div id="HidePanelControlInner">
&#9664;
</div>
</div>
<div id="ControlsCheckFiltering">
<h2>
过滤检查
</h2>
<table id="CPFilteringTable" border="1">
<tr>
<td align="left" valign="top">
<input id="Passed Checkbox" checked="checked" onclick="updateVisibleChecks(this)" type="checkbox" />

<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAHTSURBVHgBrVO9ThtBEP7mLomDFEWXpEhFbD9BlC5SUGynSkdSpoiOMt2lovXdG0CqpEpIR2cXtPxJSNAgIyoaOKBBAuEzDRYSHmb29hYbRAWjk3a1O9/PzM4B9wy6dRIHAeBFBJpioJInUIfBHeAyQZyldxL4yatwwJgRYGAvGETEPGBLlDFxgubprMM4dPIyAtNv2T0dYid2e9L9mOw+o1FKsdzf0nPP2q4IOFZFHnLEziJRJXiDuDaNF6XnUo0/k5daEMBXcMCaidFQEgUvhi0069P4WJ3QlAADL3IEYu/tKIa5IFLwUtg2a+doG6vpWu7KRzjkgA3BXrSJ3WhT7JY1h6sG3BLwOLYE/GluEt1+TxSkUqaqI2BbatY/s4ot1MofaNEqK7gxN8lduS8aWti1JSDV9ev8d6TZQU4y1XbKjf9fRPmMhvsCMxfOAf0TVuxnh6pkSDSsMrLzHuNWUMeKw06fv2e6axo3jnp5Au2dBVPWtarzkAGDdzqV16+WBCGx/7d4SjYvQeZjvmGAvJ9oHs/mj1GETlZtrCeA9zDTODoSlJuQe/xA8+RPce6PMK+cr6NempfWBJr/7LH/+skjjy4uORWKX/IzfUPc3cBDxhXE6LNA6WFItQAAAABJRU5ErkJggg==" /> 通过
</td>
<td align="left" valign="top">
&#160;
</td>

</tr>
<tr>
<td align="left" valign="top">
<input id="Failed Checkbox" checked="checked" onclick="updateVisibleChecks(this)" type="checkbox" />

<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAEDSURBVHgBrVIxCsJAENwVGzux1MZCxDKg+AYVLHxBYmXjI/IECxsr4wsshPgHEUkZxCJNLCWdnWf2SI41uYBBpzhud2fm9vYO4EcgD9x+18QXruJtvYAfCCHsiXfd5QwSsQNfIDaxUpMKF7emMxhdfOgsljkR5ahGHER0XKNrKoNYbGfJ3CQbSw3Kq0I1idu0hIc91JotrQHhtllLToI6N1Agku5Uyqc1jgpoQMTH+aRi2uvEhQZ0cmMwVDHtdYPVGvDWedu6QRI+ZkBPpBNzg+c95INUHUTcKCsuGGBAi/yJR6NnAYotlIHA+djzHfWVS5kkYmBXAJmIC2lrBYi4+C94A/a+eag5PUw2AAAAAElFTkSuQmCC" /> 失败
</td>
<td align="left" valign="top">
&#160;
</td>

</tr>
<tr>
<td align="left" valign="top">
<input id="Warning Checkbox" checked="checked" onclick="updateVisibleChecks(this)" type="checkbox" />

<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAD3SURBVHgBzZI7EsFQFIb/I4oQRTSUsgOlNlqPGTtgB+yAHRg9Ywka/c0WFGp0aCgYxuvIZcwk8phkRuGr7uPc///P3AP8NVY9vbBqmgirSQRdiIrWApNhL01RTXUQB9FQDel+Wi/5tFmynWInTF2PnIDuSlu6q/kC1FxBHunIXDqRBKQ7GJ5iArX9UngTXJUe/NFJu/ZDBaQ7EZoIgtAStYwZKEA3ZeLcnzcrnLerLw3uuvcfd/vbKIExIsCgcnl6sFwJSHErS0rDOUqjuUeAiMeuFhxDEw2G8RmuVwtyaGIJvNmb02M2+RJ80MDuvxjnNT8wwy94AjZHSy+aGaDFAAAAAElFTkSuQmCC" /> 警告
</td>
<td align="left" valign="top">
&#160;
</td>

</tr>
<tr>
<td align="left" valign="top">
<input id="Not Run Checkbox" checked="checked" onclick="updateVisibleChecks(this)" type="checkbox" />

<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAADhSURBVHgBtZI/CoMwFIdfQg/QyU3o4ubopjdw8gS9heANdOsVegGv0A66F3RzqOCkU92ypUYSCM0flOIHIY/k5cdHEoA/QaLI8/y9TBdLb48xTtI0fZkCqOd5wMYv8zxDXdes/FBKkyzLnmIPy43TNMEwDNrBOSOEHkVRXMXCSQ5wHAd83wcdYRiuc9M00LbtbSnvSgAz4KpGxnFcTYwGQRBYA7gBaAN2GOgDXNeFKIqsAVVVmQ3YhnTjWgghZgP2AocbsE9lNYjjWHuwLEvouk5ZVwxkvS3IX7mH7ezptfMFbNhwT85bFwwAAAAASUVORK5CYII=" /> 未运行
</td>
<td align="left" valign="top">
&#160;
</td>

</tr>
<tr>
<td align="left" valign="top">
<input id="Justified Checkbox" checked="checked" onclick="updateVisibleChecks(this)" type="checkbox" />

<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAADqSURBVHgBpZKvC8JAFMffZAPFslXTLBYNitVimBYx+A/of+CibYs2rcZVg4gYBYtFUJdWLFsxb0UUDOod3Insx43dBw7e4/i++753T3h/AQ5ywIlIAuvsgb65QPB8JQpUpQimVoNhU8W5QFooT7fg+XdIg1yQwDcHOKYtsMToRb1VwXHw+LlMNQMkRrbXzi10F1kAvWSPOyDnJSpuL/aRLsWoAvPDFeolBWy9i/M4cawDxGh5BOvkJopjHRDMnQMs/hy4kx4+afNQgSzQRVKMFXMLCWgbiRPqYNZv4AsW6GsNrRp2kBXuGXwAIu1X3q8Yjd0AAAAASUVORK5CYII=" /> 已申述
</td>
<td align="left" valign="top">
&#160;
</td>

</tr>
<tr>
<td align="left" valign="top">
<input id="Information Checkbox" checked="checked" onclick="updateVisibleChecks(this)" type="checkbox" />

<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsSAAALEgHS3X78AAACVElEQVQ4jV1TTWtTQRQ99868NKWUihRauypBkLppNy20CFl2adE/8HYuxIXEnX+hi/oLDOhWydLiyoVZtC6ahVCskUohGMWPSGyT997MlTvvJTZeGJiPe849cz9IRHDZKrvNmJhjEFchHhABGQtxWQvAXru2Wb/sPyao7DbXiKhObFaJDfT+7s2rADFevP8GCCA+U1clitu1zaMxQWW3uUzERyCaYxtBCTaWZvD8zvVAfn//DK9PfqgK+CwFID0RWfv0aOuUAwtRg4wZg1XyQeccjQ89HH8f4rCbgqMpkC3BTJUVMQegoVjzbHY7Jjb3wAZsbADrWpkvw7NFu5fBiaCbGJhoKihSJRC/+KR59tmKSExEITKIQtIAwuNbS1i/Nh0Ah1+GiPe/hne2paDGi1ei2BJRNXzDGDBbgBlsbU42TjWBiMMbYGFK5VzJoF8tcsAgMrkvc1ATv+riXXeYK+gmIBuFL6oC3QfVetYyidZb66QRiIuoHL5SJHmiV8JZgwaCAKZCMo0gkyAqzoXP6J1YCbQxqICOyQolNBE2B45UEsEN+i0l2FP1Pksg3uUgcUU1CtO9D62Y+4iDSy5UwR5rb4tLW9phWl+XDEI6nm7P48aVKBDcrpTD8mmCtP8LLhlq4lsfH67XbSiHyA7EH/ksmWNmuHSI9YW8adSWZgwWS1mYj+z8N8T7HiA7/w/TMhvbIBOtclTCg40FrMxPYzYidP44vDz+ibcnHYiXFhF22rXN0wmCkek4s41i79IqmxLEpTDlGWQX/TdEqLdrW//GGcBfdA38qIpY8foAAAAASUVORK5CYII=" /> 信息
</td>
<td align="left" valign="top">
&#160;
</td>

</tr>
<tr>
<td align="left" valign="top">
<input id="Incomplete Checkbox" checked="checked" onclick="updateVisibleChecks(this)" type="checkbox" />

<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAEQSURBVHgBrVI7csIwEH3rMK7dZdI5J0jKZNLQJJM+aRPICRJuEG7gI+Ch5QD8ChoGOnwEOobONRgvWo0QDB9jD7xGK+3u05P2AReC9g+4XPbgLn5VWAXDN2WRykS4SevUHk9PEvDbSwUpB+rUw3HEcLhOnVFwQMCvz+pWCpBLN1WpOwwtAb8/+UicScbNh0qW7j0NBrGjtwn9F2gWeCgt/iRwjKYHFAWhIkvJbB712mwBt3fZjfMZ8PUpkb+jwIAJZ7FXYxTwVCV8fH+gAKKtgpRCFMcOQeIGynVx7lapVa60BDJPUFrLTQC2lrafSL1xA8y1TCWSW+GH+kesbGu0K5WxxBub8UJ/cihP1WqviTVjXFeRN9ClYgAAAABJRU5ErkJggg==" /> 未完成
</td>
<td align="left" valign="top">
&#160;
</td>

</tr>
<tr>
<td align="left" valign="top">
&#160;
</td>
<td align="left" valign="top">
&#160;
</td>

</tr>

</table>
</div>
<div id="TextFilter">
<input id="TxtFilterInput" onkeyup="filterByText(event)" onfocus="if (this.value=='按检查名称搜索'){ this.value=''; this.style.color='black';}" onblur="if (this.value==''){ this.value='按检查名称搜索'; this.style.color='gray';}" value="按检查名称搜索" type="text" title="隐藏其标题中没有关键字的检查。" />

</div>
<div id="ControlsTOC">
<h2>
导航
</h2>
<div id="TOCScrollableArea">
<div class="ControlPanelTextControl" onmouseover="this.style.cursor='pointer'" onclick="navigateToElement('FolderControl_com.mathworks.cgo.group')">
 代码生成顾问</div>
</div>
</div>
<div id="ControlsView">
<h2>
视图
</h2>
<div class="ControlPanelTextControl" title="滚动到报告的顶部" onmouseover="this.style.cursor='pointer'" onclick="navigateToElement('top')">
<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAARAQMAAADqlG66AAAAA3NCSVQICAjb4U/gAAAABlBMVEX///9Tktdg+Ox4AAAAAnRSTlMA/1uRIrUAAAAJcEhZcwAACxIAAAsSAdLdfvwAAAAWdEVYdENyZWF0aW9uIFRpbWUAMTIvMTgvMDiz53+6AAAAGHRFWHRTb2Z0d2FyZQBBZG9iZSBGaXJld29ya3NPsx9OAAAEEXRFWHRYTUw6Y29tLmFkb2JlLnhtcAA8P3hwYWNrZXQgYmVnaW49IiAgICIgaWQ9Ilc1TTBNcENlaGlIenJlU3pOVGN6a2M5ZCI/Pgo8eDp4bXBtZXRhIHhtbG5zOng9ImFkb2JlOm5zOm1ldGEvIiB4OnhtcHRrPSJBZG9iZSBYTVAgQ29yZSA0LjEtYzAzNCA0Ni4yNzI5NzYsIFNhdCBKYW4gMjcgMjAwNyAyMjozNzozNyAgICAgICAgIj4KICAgPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4KICAgICAgPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIKICAgICAgICAgICAgeG1sbnM6eGFwPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIj4KICAgICAgICAgPHhhcDpDcmVhdG9yVG9vbD5BZG9iZSBGaXJld29ya3MgQ1MzPC94YXA6Q3JlYXRvclRvb2w+CiAgICAgICAgIDx4YXA6Q3JlYXRlRGF0ZT4yMDA4LTEyLTE4VDIyOjA2OjAxWjwveGFwOkNyZWF0ZURhdGU+CiAgICAgICAgIDx4YXA6TW9kaWZ5RGF0ZT4yMDA4LTEyLTE4VDIyOjA4OjQzWjwveGFwOk1vZGlmeURhdGU+CiAgICAgIDwvcmRmOkRlc2NyaXB0aW9uPgogICAgICA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIgogICAgICAgICAgICB4bWxuczpkYz0iaHR0cDovL3B1cmwub3JnL2RjL2VsZW1lbnRzLzEuMS8iPgogICAgICAgICA8ZGM6Zm9ybWF0PmltYWdlL3BuZzwvZGM6Zm9ybWF0PgogICAgICA8L3JkZjpEZXNjcmlwdGlvbj4KICAgPC9yZGY6UkRGPgo8L3g6eG1wbWV0YT4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA9F2ciAAAAIElEQVQImWNgQAP8f5A4BxgYGB8wMDB/YGBg/4EmhwAAvEcFqqXqgcsAAAAASUVORK5CYII="  id="CPToTopImg"/>滚动到顶部</div>
<div id="ExpandCollapseAll" class="ControlPanelTextControl" onmouseover="this.style.cursor='pointer'" onclick="expandCollapseAll(this)" title="展开/折叠所有可折叠的检查内容。">
显示检查详细信息</div>
</div>
</div>
<!-- mdladv_ignore_finish -->
<!-- mdladv_ignore_start --><div class="ReportContent" id="pipelined_TOP"><!-- mdladv_ignore_finish --><table class="AdvTableNoBorder" width="100%" border="0">
<tr>
<td colspan="2" align="center">
<b>
代码生成顾问报告 - <font color="#800000">pipelined_TOP.slx</font>
</b>

</td>

</tr>
<tr>
<td align="left" valign="top">
<b>
Simulink 版本: <font color="#800000">23.2</font>
</b>

</td>
<td align="right" valign="top">
<b>
模型版本: <font color="#800000">1.24</font>
</b>

</td>

</tr>
<tr>
<td align="left" valign="top">
<b>
系统: <font color="#800000">pipelined_TOP</font>
</b>

</td>
<td align="right" valign="top">
<b>
当前运行: <font color="#800000">2025/06/27 12:56:58</font>
</b>

</td>

</tr>
<tr>
<td align="left" valign="top">
<b>
视为引用模型: <font color="#800000">off</font>
</b>

</td>
<td align="right" valign="top">
&#160;
</td>

</tr>

</table>
<br /><font color="#800000"><b>运行摘要</b></font><br /><table class="AdvTableNoBorder" width="60%" border="0">
<tr>
<th align="left" valign="top">
<b>
未完成
</b>

</th>
<th align="left" valign="top">
<b>
失败
</b>

</th>
<th align="left" valign="top">
<b>
警告
</b>

</th>
<th align="left" valign="top">
<b>
已申述
</b>

</th>
<th align="left" valign="top">
<b>
通过
</b>

</th>
<th align="left" valign="top">
<b>
信息
</b>

</th>
<th align="left" valign="top">
<b>
未运行
</b>

</th>
<th align="left" valign="top">
<b>
合计
</b>

</th>

</tr>
<tr>
<td align="left" valign="top">
&#160;&#160;<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAEQSURBVHgBrVI7csIwEH3rMK7dZdI5J0jKZNLQJJM+aRPICRJuEG7gI+Ch5QD8ChoGOnwEOobONRgvWo0QDB9jD7xGK+3u05P2AReC9g+4XPbgLn5VWAXDN2WRykS4SevUHk9PEvDbSwUpB+rUw3HEcLhOnVFwQMCvz+pWCpBLN1WpOwwtAb8/+UicScbNh0qW7j0NBrGjtwn9F2gWeCgt/iRwjKYHFAWhIkvJbB712mwBt3fZjfMZ8PUpkb+jwIAJZ7FXYxTwVCV8fH+gAKKtgpRCFMcOQeIGynVx7lapVa60BDJPUFrLTQC2lrafSL1xA8y1TCWSW+GH+kesbGu0K5WxxBub8UJ/cihP1WqviTVjXFeRN9ClYgAAAABJRU5ErkJggg=="  /> 0
</td>
<td align="left" valign="top">
&#160;&#160;<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAEDSURBVHgBrVIxCsJAENwVGzux1MZCxDKg+AYVLHxBYmXjI/IECxsr4wsshPgHEUkZxCJNLCWdnWf2SI41uYBBpzhud2fm9vYO4EcgD9x+18QXruJtvYAfCCHsiXfd5QwSsQNfIDaxUpMKF7emMxhdfOgsljkR5ahGHER0XKNrKoNYbGfJ3CQbSw3Kq0I1idu0hIc91JotrQHhtllLToI6N1Agku5Uyqc1jgpoQMTH+aRi2uvEhQZ0cmMwVDHtdYPVGvDWedu6QRI+ZkBPpBNzg+c95INUHUTcKCsuGGBAi/yJR6NnAYotlIHA+djzHfWVS5kkYmBXAJmIC2lrBYi4+C94A/a+eag5PUw2AAAAAElFTkSuQmCC"  /> 4
</td>
<td align="left" valign="top">
&#160;&#160;<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAD3SURBVHgBzZI7EsFQFIb/I4oQRTSUsgOlNlqPGTtgB+yAHRg9Ywka/c0WFGp0aCgYxuvIZcwk8phkRuGr7uPc///P3AP8NVY9vbBqmgirSQRdiIrWApNhL01RTXUQB9FQDel+Wi/5tFmynWInTF2PnIDuSlu6q/kC1FxBHunIXDqRBKQ7GJ5iArX9UngTXJUe/NFJu/ZDBaQ7EZoIgtAStYwZKEA3ZeLcnzcrnLerLw3uuvcfd/vbKIExIsCgcnl6sFwJSHErS0rDOUqjuUeAiMeuFhxDEw2G8RmuVwtyaGIJvNmb02M2+RJ80MDuvxjnNT8wwy94AjZHSy+aGaDFAAAAAElFTkSuQmCC"  /> 2
</td>
<td align="left" valign="top">
&#160;&#160;<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAADqSURBVHgBpZKvC8JAFMffZAPFslXTLBYNitVimBYx+A/of+CibYs2rcZVg4gYBYtFUJdWLFsxb0UUDOod3Insx43dBw7e4/i++753T3h/AQ5ywIlIAuvsgb65QPB8JQpUpQimVoNhU8W5QFooT7fg+XdIg1yQwDcHOKYtsMToRb1VwXHw+LlMNQMkRrbXzi10F1kAvWSPOyDnJSpuL/aRLsWoAvPDFeolBWy9i/M4cawDxGh5BOvkJopjHRDMnQMs/hy4kx4+afNQgSzQRVKMFXMLCWgbiRPqYNZv4AsW6GsNrRp2kBXuGXwAIu1X3q8Yjd0AAAAASUVORK5CYII="  /> 0
</td>
<td align="left" valign="top">
&#160;&#160;<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAHTSURBVHgBrVO9ThtBEP7mLomDFEWXpEhFbD9BlC5SUGynSkdSpoiOMt2lovXdG0CqpEpIR2cXtPxJSNAgIyoaOKBBAuEzDRYSHmb29hYbRAWjk3a1O9/PzM4B9wy6dRIHAeBFBJpioJInUIfBHeAyQZyldxL4yatwwJgRYGAvGETEPGBLlDFxgubprMM4dPIyAtNv2T0dYid2e9L9mOw+o1FKsdzf0nPP2q4IOFZFHnLEziJRJXiDuDaNF6XnUo0/k5daEMBXcMCaidFQEgUvhi0069P4WJ3QlAADL3IEYu/tKIa5IFLwUtg2a+doG6vpWu7KRzjkgA3BXrSJ3WhT7JY1h6sG3BLwOLYE/GluEt1+TxSkUqaqI2BbatY/s4ot1MofaNEqK7gxN8lduS8aWti1JSDV9ev8d6TZQU4y1XbKjf9fRPmMhvsCMxfOAf0TVuxnh6pkSDSsMrLzHuNWUMeKw06fv2e6axo3jnp5Au2dBVPWtarzkAGDdzqV16+WBCGx/7d4SjYvQeZjvmGAvJ9oHs/mj1GETlZtrCeA9zDTODoSlJuQe/xA8+RPce6PMK+cr6NempfWBJr/7LH/+skjjy4uORWKX/IzfUPc3cBDxhXE6LNA6WFItQAAAABJRU5ErkJggg=="  /> 7
</td>
<td align="left" valign="top">
&#160;&#160;<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsSAAALEgHS3X78AAACVElEQVQ4jV1TTWtTQRQ99868NKWUihRauypBkLppNy20CFl2adE/8HYuxIXEnX+hi/oLDOhWydLiyoVZtC6ahVCskUohGMWPSGyT997MlTvvJTZeGJiPe849cz9IRHDZKrvNmJhjEFchHhABGQtxWQvAXru2Wb/sPyao7DbXiKhObFaJDfT+7s2rADFevP8GCCA+U1clitu1zaMxQWW3uUzERyCaYxtBCTaWZvD8zvVAfn//DK9PfqgK+CwFID0RWfv0aOuUAwtRg4wZg1XyQeccjQ89HH8f4rCbgqMpkC3BTJUVMQegoVjzbHY7Jjb3wAZsbADrWpkvw7NFu5fBiaCbGJhoKihSJRC/+KR59tmKSExEITKIQtIAwuNbS1i/Nh0Ah1+GiPe/hne2paDGi1ei2BJRNXzDGDBbgBlsbU42TjWBiMMbYGFK5VzJoF8tcsAgMrkvc1ATv+riXXeYK+gmIBuFL6oC3QfVetYyidZb66QRiIuoHL5SJHmiV8JZgwaCAKZCMo0gkyAqzoXP6J1YCbQxqICOyQolNBE2B45UEsEN+i0l2FP1Pksg3uUgcUU1CtO9D62Y+4iDSy5UwR5rb4tLW9phWl+XDEI6nm7P48aVKBDcrpTD8mmCtP8LLhlq4lsfH67XbSiHyA7EH/ksmWNmuHSI9YW8adSWZgwWS1mYj+z8N8T7HiA7/w/TMhvbIBOtclTCg40FrMxPYzYidP44vDz+ibcnHYiXFhF22rXN0wmCkek4s41i79IqmxLEpTDlGWQX/TdEqLdrW//GGcBfdA38qIpY8foAAAAASUVORK5CYII="  /> 0
</td>
<td align="left" valign="top">
&#160;&#160;<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAADhSURBVHgBtZI/CoMwFIdfQg/QyU3o4ubopjdw8gS9heANdOsVegGv0A66F3RzqOCkU92ypUYSCM0flOIHIY/k5cdHEoA/QaLI8/y9TBdLb48xTtI0fZkCqOd5wMYv8zxDXdes/FBKkyzLnmIPy43TNMEwDNrBOSOEHkVRXMXCSQ5wHAd83wcdYRiuc9M00LbtbSnvSgAz4KpGxnFcTYwGQRBYA7gBaAN2GOgDXNeFKIqsAVVVmQ3YhnTjWgghZgP2AocbsE9lNYjjWHuwLEvouk5ZVwxkvS3IX7mH7ezptfMFbNhwT85bFwwAAAAASUVORK5CYII="  /> 0
</td>
<td align="left" valign="top">
 13
</td>

</tr>

</table>
<!-- Service Status Flag -->
<!-- mdladv_ignore_start --><br /><br /><font color="#800000"><b><!-- mdladv_ignore_start --><span class="FolderControl" id="FolderControl_com.mathworks.cgo.group" onclick="MATableShrink(this,'com.mathworks.cgo.group')" onmouseover="this.style.cursor = 'pointer'">
<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAABGdBTUEAALGOfPtRkwAAACBjSFJNAAB6JQAAgIMAAPn/AACA6QAAdTAAAOpgAAA6mAAAF2+SX8VGAAAAhklEQVR42mL8//8/AyUAIICYGCgEAAFEsQEAAUSxAQABxIIu0NTURDBQ6urqGGFsgABiwaagpqYOp+aWliYUPkAAUewFgACi2ACAAGLBKcGCafafP/8wxAACCKcB2BRjAwABRLEXAAKIYgMAAoiFmKjCBwACiJHSzAQQQBR7ASCAKDYAIMAAUtQUow+YsTsAAAAASUVORK5CYII="  /></span>
<!-- mdladv_ignore_finish --> 代码生成顾问</b></font><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --><div name = "com.mathworks.cgo.group"  id = "com.mathworks.cgo.group" class="FolderContent"><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --><div class="EmptyFolderMessage" style="display:none;">
所有检查都隐藏。
</div>
<!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --><div name = "Warning Check" id = "Warning Check" class="WarningCheck" style="margin-left: 10pt;"> <!-- mdladv_ignore_finish -->
<p><hr /></p>  <a name="CheckRecord_36"></a><div class="CheckHeader" id="Header_mathworks.codegen.CodeGenSanity">
<!-- mdladv_ignore_start --><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAD3SURBVHgBzZI7EsFQFIb/I4oQRTSUsgOlNlqPGTtgB+yAHRg9Ywka/c0WFGp0aCgYxuvIZcwk8phkRuGr7uPc///P3AP8NVY9vbBqmgirSQRdiIrWApNhL01RTXUQB9FQDel+Wi/5tFmynWInTF2PnIDuSlu6q/kC1FxBHunIXDqRBKQ7GJ5iArX9UngTXJUe/NFJu/ZDBaQ7EZoIgtAStYwZKEA3ZeLcnzcrnLerLw3uuvcfd/vbKIExIsCgcnl6sFwJSHErS0rDOUqjuUeAiMeuFhxDEw2G8RmuVwtyaGIJvNmb02M2+RJ80MDuvxjnNT8wwy94AjZHSy+aGaDFAAAAAElFTkSuQmCC"  />&#160;<!-- mdladv_ignore_finish --><span class="CheckHeading" id="Heading_mathworks.codegen.CodeGenSanity">
对照代码生成目标检查模型配置设置</span>
<!-- mdladv_ignore_start --><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --><!-- mdladv_ignore_finish --></div>
<!-- mdladv_ignore_start --><div class="subsection"><!-- mdladv_ignore_finish --><p /><p /><p>
<br /><b>当前目标:</b> <font color="#0000FF">执行效率</font><br><br><br>以下参数值不是针对所选目标优化的。<br><br>要自动修复警告，请点击 '修改参数' 按钮，然后重新运行检查。要手动修复警告，请点击参数超链接打开 "配置参数" 对话框，并手动应用推荐值。<table class="AdvTable" border="1">
<tr>
<th align="left" valign="top">
<b>
参数
</b>

</th>
<th align="left" valign="top">
<b>
当前值
</b>

</th>
<th align="left" valign="top">
<b>
推荐值
</b>

</th>

</tr>
<tr>
<td align="left" valign="top">
<a href="matlab: modeladvisorprivate openCSAndHighlight pipelined_TOP MatFileLogging"> MAT 文件记录</a>
</td>
<td align="left" valign="top">
on
</td>
<td align="left" valign="top">
off
</td>

</tr>
<tr>
<td align="left" valign="top">
<a href="matlab: modeladvisorprivate openCSAndHighlight pipelined_TOP DefaultParameterBehavior"> 默认参数行为</a>
</td>
<td align="left" valign="top">
Tunable
</td>
<td align="left" valign="top">
Inlined
</td>

</tr>
<tr>
<td align="left" valign="top">
<a href="matlab: modeladvisorprivate openCSAndHighlight pipelined_TOP GainParamInheritBuiltInType"> 增益参数继承无损的内置整数类型</a>
</td>
<td align="left" valign="top">
off
</td>
<td align="left" valign="top">
on
</td>

</tr>
<tr>
<td align="left" valign="top">
<a href="matlab: modeladvisorprivate openCSAndHighlight pipelined_TOP UseFloatMulNetSlope"> 使用浮点乘法处理净斜率校正</a>
</td>
<td align="left" valign="top">
off
</td>
<td align="left" valign="top">
on
</td>

</tr>
<tr>
<td align="left" valign="top">
<a href="matlab: modeladvisorprivate openCSAndHighlight pipelined_TOP InlineInvariantSignals"> 内联不变信号</a>
</td>
<td align="left" valign="top">
off
</td>
<td align="left" valign="top">
on
</td>

</tr>
<tr>
<td align="left" valign="top">
<a href="matlab: modeladvisorprivate openCSAndHighlight pipelined_TOP EfficientFloat2IntCast"> 删除从浮点到整数转换中将超出范围值绕回的代码</a>
</td>
<td align="left" valign="top">
off
</td>
<td align="left" valign="top">
on
</td>

</tr>
<tr>
<td align="left" valign="top">
<a href="matlab: modeladvisorprivate openCSAndHighlight pipelined_TOP BuildConfiguration"> 编译配置</a>
</td>
<td align="left" valign="top">
Faster Builds
</td>
<td align="left" valign="top">
Faster Runs
</td>

</tr>
<tr>
<td align="left" valign="top">
<a href="matlab: modeladvisorprivate openCSAndHighlight pipelined_TOP SupportNonFinite"> 支持非有限数</a>
</td>
<td align="left" valign="top">
on
</td>
<td align="left" valign="top">
off
</td>

</tr>

</table>
<br /><br />_________________________________________________________________________________________
</p>
<!-- mdladv_ignore_start --></div><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --></div><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --><div name = "Passed Check"  id = "Passed Check" class="PassedCheck" style="margin-left: 10pt;"><!-- mdladv_ignore_finish -->
<p><hr /></p>  <a name="CheckRecord_20"></a><div class="CheckHeader" id="Header_mathworks.design.OptBusVirtuality">
<!-- mdladv_ignore_start --><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAHTSURBVHgBrVO9ThtBEP7mLomDFEWXpEhFbD9BlC5SUGynSkdSpoiOMt2lovXdG0CqpEpIR2cXtPxJSNAgIyoaOKBBAuEzDRYSHmb29hYbRAWjk3a1O9/PzM4B9wy6dRIHAeBFBJpioJInUIfBHeAyQZyldxL4yatwwJgRYGAvGETEPGBLlDFxgubprMM4dPIyAtNv2T0dYid2e9L9mOw+o1FKsdzf0nPP2q4IOFZFHnLEziJRJXiDuDaNF6XnUo0/k5daEMBXcMCaidFQEgUvhi0069P4WJ3QlAADL3IEYu/tKIa5IFLwUtg2a+doG6vpWu7KRzjkgA3BXrSJ3WhT7JY1h6sG3BLwOLYE/GluEt1+TxSkUqaqI2BbatY/s4ot1MofaNEqK7gxN8lduS8aWti1JSDV9ev8d6TZQU4y1XbKjf9fRPmMhvsCMxfOAf0TVuxnh6pkSDSsMrLzHuNWUMeKw06fv2e6axo3jnp5Au2dBVPWtarzkAGDdzqV16+WBCGx/7d4SjYvQeZjvmGAvJ9oHs/mj1GETlZtrCeA9zDTODoSlJuQe/xA8+RPce6PMK+cr6NempfWBJr/7LH/+skjjy4uORWKX/IzfUPc3cBDxhXE6LNA6WFItQAAAABJRU5ErkJggg=="  />&#160;<!-- mdladv_ignore_finish --><span class="CheckHeading" id="Heading_mathworks.design.OptBusVirtuality">
检查以实现最佳总线虚拟化</span>
<!-- mdladv_ignore_start --><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --><!-- mdladv_ignore_finish --></div>
<!-- mdladv_ignore_start --><div class="subsection"><!-- mdladv_ignore_finish --><p /><p /><font color="#008000">通过</font><!-- mdladv_ignore_start --></div><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --></div><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --><div name = "Warning Check" id = "Warning Check" class="WarningCheck" style="margin-left: 10pt;"> <!-- mdladv_ignore_finish -->
<p><hr /></p>  <a name="CheckRecord_7"></a><div class="CheckHeader" id="Header_mathworks.codegen.QuestionableBlks">
<!-- mdladv_ignore_start --><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAD3SURBVHgBzZI7EsFQFIb/I4oQRTSUsgOlNlqPGTtgB+yAHRg9Ywka/c0WFGp0aCgYxuvIZcwk8phkRuGr7uPc///P3AP8NVY9vbBqmgirSQRdiIrWApNhL01RTXUQB9FQDel+Wi/5tFmynWInTF2PnIDuSlu6q/kC1FxBHunIXDqRBKQ7GJ5iArX9UngTXJUe/NFJu/ZDBaQ7EZoIgtAStYwZKEA3ZeLcnzcrnLerLw3uuvcfd/vbKIExIsCgcnl6sFwJSHErS0rDOUqjuUeAiMeuFhxDEw2G8RmuVwtyaGIJvNmb02M2+RJ80MDuvxjnNT8wwy94AjZHSy+aGaDFAAAAAElFTkSuQmCC"  />&#160;<!-- mdladv_ignore_finish --><span class="CheckHeading" id="Heading_mathworks.codegen.QuestionableBlks">
标识指定系统中不可靠的模块</span>
<!-- mdladv_ignore_start --><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --><!-- mdladv_ignore_finish --></div>
<!-- mdladv_ignore_start --><div class="subsection"><!-- mdladv_ignore_finish --><p /><p>
<b>
检查模型中是否存在不推荐用于生产代码生成的构造
</b>
<br />标识不推荐用于代码生成的模块。<br /><br /><b>
<font color="Orange">
警告 
</font>

</b>
<br />以下构造不推荐用于生产代码生成<ul>
<li>
<a href="matlab: modeladvisorprivate hiliteSystem pipelined_TOPzfManualzsSwitch" title="pipelined_TOP/Manual Switch">
pipelined_TOP/Manual Switch
</a>

</li>
<li>
<a href="matlab: modeladvisorprivate hiliteSystem pipelined_TOPzfRepeatingzrSequence1" title="pipelined_TOP/Repeating
Sequence1">
pipelined_TOP/Repeating
Sequence1
</a>

</li>
<li>
<a href="matlab: modeladvisorprivate hiliteSystem pipelined_TOPzfSinezsWave" title="pipelined_TOP/Sine Wave">
pipelined_TOP/Sine Wave
</a>

</li>
<li>
<a href="matlab: modeladvisorprivate hiliteSystem pipelined_TOPzfCLKh" title="pipelined_TOP/CLKh">
pipelined_TOP/CLKh
</a>

</li>
<li>
<a href="matlab: modeladvisorprivate hiliteSystem pipelined_TOPzfCLKs" title="pipelined_TOP/CLKs">
pipelined_TOP/CLKs
</a>

</li>

</ul>
<b>
建议采取的操作
</b>
<br />虽然 Embedded Coder 支持这些模块，但不推荐将这些模块用于生产代码部署。有关详细信息，请参阅表中的 "代码生成支持" 列:<a href="matlab: showblockdatatypetable">  Simulink 模块支持</a>. 查看这些模块的代码生成支持说明，并遵循给出的建议。<br />_________________________________________________________________________________________
</p>
<p /><p>
<b>
检查 Gain 模块的使用
</b>
<br />标识其值等于 1 的 Gain 模块<br /><br /><b>
<font color="Orange">
警告 
</font>

</b>
<br />模型或子系统包含其值等于 1 的 Gain 模块<ul>
<li>
<a href="matlab: modeladvisorprivate hiliteSystem pipelined_TOPzf10bitzsDACzfGain23" title="pipelined_TOP/10bit DAC/Gain23">
pipelined_TOP/10bit DAC/Gain23
</a>

</li>
<li>
<a href="matlab: modeladvisorprivate hiliteSystem pipelined_TOPzfSHAzfz1CszkCpz2zfzfCs" title="pipelined_TOP/SHA/(Cs+Cp)//Cs">
pipelined_TOP/SHA/(Cs+Cp)//Cs
</a>

</li>

</ul>
<b>
建议采取的操作
</b>
<br />如果您使用这些模块作为缓冲区，您应该用 Signal Conversion 模块替换它们。
</p>
<!-- mdladv_ignore_start --></div><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --></div><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --><div name = "Passed Check"  id = "Passed Check" class="PassedCheck" style="margin-left: 10pt;"><!-- mdladv_ignore_finish -->
<p><hr /></p>  <a name="CheckRecord_14"></a><div class="CheckHeader" id="Header_mathworks.codegen.HWImplementation">
<!-- mdladv_ignore_start --><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAHTSURBVHgBrVO9ThtBEP7mLomDFEWXpEhFbD9BlC5SUGynSkdSpoiOMt2lovXdG0CqpEpIR2cXtPxJSNAgIyoaOKBBAuEzDRYSHmb29hYbRAWjk3a1O9/PzM4B9wy6dRIHAeBFBJpioJInUIfBHeAyQZyldxL4yatwwJgRYGAvGETEPGBLlDFxgubprMM4dPIyAtNv2T0dYid2e9L9mOw+o1FKsdzf0nPP2q4IOFZFHnLEziJRJXiDuDaNF6XnUo0/k5daEMBXcMCaidFQEgUvhi0069P4WJ3QlAADL3IEYu/tKIa5IFLwUtg2a+doG6vpWu7KRzjkgA3BXrSJ3WhT7JY1h6sG3BLwOLYE/GluEt1+TxSkUqaqI2BbatY/s4ot1MofaNEqK7gxN8lduS8aWti1JSDV9ev8d6TZQU4y1XbKjf9fRPmMhvsCMxfOAf0TVuxnh6pkSDSsMrLzHuNWUMeKw06fv2e6axo3jnp5Au2dBVPWtarzkAGDdzqV16+WBCGx/7d4SjYvQeZjvmGAvJ9oHs/mj1GETlZtrCeA9zDTODoSlJuQe/xA8+RPce6PMK+cr6NempfWBJr/7LH/+skjjy4uORWKX/IzfUPc3cBDxhXE6LNA6WFItQAAAABJRU5ErkJggg=="  />&#160;<!-- mdladv_ignore_finish --><span class="CheckHeading" id="Heading_mathworks.codegen.HWImplementation">
检查硬件实现</span>
<!-- mdladv_ignore_start --><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --><!-- mdladv_ignore_finish --></div>
<!-- mdladv_ignore_start --><div class="subsection"><!-- mdladv_ignore_finish --><p /><p /><p>
<b>
检查 '字节顺序' 和 '有符号整数除法舍入方式' 参数
</b>
<br />标识不一致或欠定的硬件属性，这种情况可能导致生成的代码不正确和效率低下。<br /><br /><b>
<font color="Green">
通过 
</font>

</b>
<br />目标设定一致。<br />_________________________________________________________________________________________
</p>
<p>
<b>
检查 '生产硬件' 和 '测试硬件' 是否匹配
</b>
<br />在 "配置参数" 对话框中搜索 '测试硬件与生产硬件相同'，并检查它是否已选中。如果它处于清除状态，标识目标设定是否匹配。<br /><br /><b>
<font color="Green">
通过 
</font>

</b>
<br />'测试硬件与生产硬件相同' 处于选中或清除状态，并且目标设定匹配。
</p>
<!-- mdladv_ignore_start --></div><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --></div><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --><div name = "Passed Check"  id = "Passed Check" class="PassedCheck" style="margin-left: 10pt;"><!-- mdladv_ignore_finish -->
<p><hr /></p>  <a name="CheckRecord_16"></a><div class="CheckHeader" id="Header_mathworks.codegen.SWEnvironmentSpec">
<!-- mdladv_ignore_start --><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAHTSURBVHgBrVO9ThtBEP7mLomDFEWXpEhFbD9BlC5SUGynSkdSpoiOMt2lovXdG0CqpEpIR2cXtPxJSNAgIyoaOKBBAuEzDRYSHmb29hYbRAWjk3a1O9/PzM4B9wy6dRIHAeBFBJpioJInUIfBHeAyQZyldxL4yatwwJgRYGAvGETEPGBLlDFxgubprMM4dPIyAtNv2T0dYid2e9L9mOw+o1FKsdzf0nPP2q4IOFZFHnLEziJRJXiDuDaNF6XnUo0/k5daEMBXcMCaidFQEgUvhi0069P4WJ3QlAADL3IEYu/tKIa5IFLwUtg2a+doG6vpWu7KRzjkgA3BXrSJ3WhT7JY1h6sG3BLwOLYE/GluEt1+TxSkUqaqI2BbatY/s4ot1MofaNEqK7gxN8lduS8aWti1JSDV9ev8d6TZQU4y1XbKjf9fRPmMhvsCMxfOAf0TVuxnh6pkSDSsMrLzHuNWUMeKw06fv2e6axo3jnp5Au2dBVPWtarzkAGDdzqV16+WBCGx/7d4SjYvQeZjvmGAvJ9oHs/mj1GETlZtrCeA9zDTODoSlJuQe/xA8+RPce6PMK+cr6NempfWBJr/7LH/+skjjy4uORWKX/IzfUPc3cBDxhXE6LNA6WFItQAAAABJRU5ErkJggg=="  />&#160;<!-- mdladv_ignore_finish --><span class="CheckHeading" id="Heading_mathworks.codegen.SWEnvironmentSpec">
标识不可靠的软件环境设定</span>
<!-- mdladv_ignore_start --><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --><!-- mdladv_ignore_finish --></div>
<!-- mdladv_ignore_start --><div class="subsection"><!-- mdladv_ignore_finish --><p /><p /><font color="#008000">通过</font><!-- mdladv_ignore_start --></div><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --></div><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --><div name = "Passed Check"  id = "Passed Check" class="PassedCheck" style="margin-left: 10pt;"><!-- mdladv_ignore_finish -->
<p><hr /></p>  <a name="CheckRecord_17"></a><div class="CheckHeader" id="Header_mathworks.codegen.CodeInstrumentation">
<!-- mdladv_ignore_start --><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAHTSURBVHgBrVO9ThtBEP7mLomDFEWXpEhFbD9BlC5SUGynSkdSpoiOMt2lovXdG0CqpEpIR2cXtPxJSNAgIyoaOKBBAuEzDRYSHmb29hYbRAWjk3a1O9/PzM4B9wy6dRIHAeBFBJpioJInUIfBHeAyQZyldxL4yatwwJgRYGAvGETEPGBLlDFxgubprMM4dPIyAtNv2T0dYid2e9L9mOw+o1FKsdzf0nPP2q4IOFZFHnLEziJRJXiDuDaNF6XnUo0/k5daEMBXcMCaidFQEgUvhi0069P4WJ3QlAADL3IEYu/tKIa5IFLwUtg2a+doG6vpWu7KRzjkgA3BXrSJ3WhT7JY1h6sG3BLwOLYE/GluEt1+TxSkUqaqI2BbatY/s4ot1MofaNEqK7gxN8lduS8aWti1JSDV9ev8d6TZQU4y1XbKjf9fRPmMhvsCMxfOAf0TVuxnh6pkSDSsMrLzHuNWUMeKw06fv2e6axo3jnp5Au2dBVPWtarzkAGDdzqV16+WBCGx/7d4SjYvQeZjvmGAvJ9oHs/mj1GETlZtrCeA9zDTODoSlJuQe/xA8+RPce6PMK+cr6NempfWBJr/7LH/+skjjy4uORWKX/IzfUPc3cBDxhXE6LNA6WFItQAAAABJRU5ErkJggg=="  />&#160;<!-- mdladv_ignore_finish --><span class="CheckHeading" id="Heading_mathworks.codegen.CodeInstrumentation">
标识不可靠的代码插桩(数据 I/O)</span>
<!-- mdladv_ignore_start --><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --><!-- mdladv_ignore_finish --></div>
<!-- mdladv_ignore_start --><div class="subsection"><!-- mdladv_ignore_finish --><p /><p /><font color="#008000">通过</font><!-- mdladv_ignore_start --></div><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --></div><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --><div name = "Failed Check" id = "Failed Check" class="FailedCheck" style="margin-left: 10pt;"> <!-- mdladv_ignore_finish -->
<p><hr /></p>  <a name="CheckRecord_699"></a><div class="CheckHeader" id="Header_mathworks.codegen.ExpensiveSaturationRoundingCode">
<!-- mdladv_ignore_start --><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAEDSURBVHgBrVIxCsJAENwVGzux1MZCxDKg+AYVLHxBYmXjI/IECxsr4wsshPgHEUkZxCJNLCWdnWf2SI41uYBBpzhud2fm9vYO4EcgD9x+18QXruJtvYAfCCHsiXfd5QwSsQNfIDaxUpMKF7emMxhdfOgsljkR5ahGHER0XKNrKoNYbGfJ3CQbSw3Kq0I1idu0hIc91JotrQHhtllLToI6N1Agku5Uyqc1jgpoQMTH+aRi2uvEhQZ0cmMwVDHtdYPVGvDWedu6QRI+ZkBPpBNzg+c95INUHUTcKCsuGGBAi/yJR6NnAYotlIHA+djzHfWVS5kkYmBXAJmIC2lrBYi4+C94A/a+eag5PUw2AAAAAElFTkSuQmCC"  />&#160;<!-- mdladv_ignore_finish --><span class="CheckHeading" id="Heading_mathworks.codegen.ExpensiveSaturationRoundingCode">
标识会生成高成本舍入代码的模块</span>
<!-- mdladv_ignore_start --><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --><!-- mdladv_ignore_finish --></div>
<!-- mdladv_ignore_start --><div class="subsection"><!-- mdladv_ignore_finish --><p /><p /><font color="#FF0000">Error occurred during model compile.<br /><br /><font color="Red"><br /><b>注意: </b>此检查适用于为代码生成配置的模型。<br /></font><br /><br/><br/>"FixedStepDiscrete" 求解器不能用于仿真模块图 '<a href="matlab:open_system ('pipelined_TOP')">pipelined_TOP</a>'，因为它包含连续状态</font><!-- mdladv_ignore_start --></div><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --></div><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --><div name = "Failed Check" id = "Failed Check" class="FailedCheck" style="margin-left: 10pt;"> <!-- mdladv_ignore_finish -->
<p><hr /></p>  <a name="CheckRecord_698"></a><div class="CheckHeader" id="Header_mathworks.codegen.QuestionableFxptOperations">
<!-- mdladv_ignore_start --><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAEDSURBVHgBrVIxCsJAENwVGzux1MZCxDKg+AYVLHxBYmXjI/IECxsr4wsshPgHEUkZxCJNLCWdnWf2SI41uYBBpzhud2fm9vYO4EcgD9x+18QXruJtvYAfCCHsiXfd5QwSsQNfIDaxUpMKF7emMxhdfOgsljkR5ahGHER0XKNrKoNYbGfJ3CQbSw3Kq0I1idu0hIc91JotrQHhtllLToI6N1Agku5Uyqc1jgpoQMTH+aRi2uvEhQZ0cmMwVDHtdYPVGvDWedu6QRI+ZkBPpBNzg+c95INUHUTcKCsuGGBAi/yJR6NnAYotlIHA+djzHfWVS5kkYmBXAJmIC2lrBYi4+C94A/a+eag5PUw2AAAAAElFTkSuQmCC"  />&#160;<!-- mdladv_ignore_finish --><span class="CheckHeading" id="Heading_mathworks.codegen.QuestionableFxptOperations">
标识不可靠的定点运算</span>
<!-- mdladv_ignore_start --><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --><!-- mdladv_ignore_finish --></div>
<!-- mdladv_ignore_start --><div class="subsection"><!-- mdladv_ignore_finish --><p /><p /><font color="#FF0000">Error occurred during model compile.<br /><br /><font color="Red"><br /><b>注意: </b>此检查适用于为代码生成配置的模型。<br /></font><br /><br/><br/>"FixedStepDiscrete" 求解器不能用于仿真模块图 '<a href="matlab:open_system ('pipelined_TOP')">pipelined_TOP</a>'，因为它包含连续状态</font><!-- mdladv_ignore_start --></div><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --></div><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --><div name = "Passed Check"  id = "Passed Check" class="PassedCheck" style="margin-left: 10pt;"><!-- mdladv_ignore_finish -->
<p><hr /></p>  <a name="CheckRecord_5"></a><div class="CheckHeader" id="Header_mathworks.codegen.cgsl_0101">
<!-- mdladv_ignore_start --><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAHTSURBVHgBrVO9ThtBEP7mLomDFEWXpEhFbD9BlC5SUGynSkdSpoiOMt2lovXdG0CqpEpIR2cXtPxJSNAgIyoaOKBBAuEzDRYSHmb29hYbRAWjk3a1O9/PzM4B9wy6dRIHAeBFBJpioJInUIfBHeAyQZyldxL4yatwwJgRYGAvGETEPGBLlDFxgubprMM4dPIyAtNv2T0dYid2e9L9mOw+o1FKsdzf0nPP2q4IOFZFHnLEziJRJXiDuDaNF6XnUo0/k5daEMBXcMCaidFQEgUvhi0069P4WJ3QlAADL3IEYu/tKIa5IFLwUtg2a+doG6vpWu7KRzjkgA3BXrSJ3WhT7JY1h6sG3BLwOLYE/GluEt1+TxSkUqaqI2BbatY/s4ot1MofaNEqK7gxN8lduS8aWti1JSDV9ev8d6TZQU4y1XbKjf9fRPmMhvsCMxfOAf0TVuxnh6pkSDSsMrLzHuNWUMeKw06fv2e6axo3jnp5Au2dBVPWtarzkAGDdzqV16+WBCGx/7d4SjYvQeZjvmGAvJ9oHs/mj1GETlZtrCeA9zDTODoSlJuQe/xA8+RPce6PMK+cr6NempfWBJr/7LH/+skjjy4uORWKX/IzfUPc3cBDxhXE6LNA6WFItQAAAABJRU5ErkJggg=="  />&#160;<!-- mdladv_ignore_finish --><span class="CheckHeading" id="Heading_mathworks.codegen.cgsl_0101">
标识使用从 1 开始的索引的模块</span>
<!-- mdladv_ignore_start --><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --><!-- mdladv_ignore_finish --></div>
<!-- mdladv_ignore_start --><div class="subsection"><!-- mdladv_ignore_finish --><p /><p /><p>
检查模型中是否存在配置为使用从 1 开始的索引的模块<br /><br /><b>
<font color="Green">
通过 
</font>

</b>
<br />模型中的所有模块都使用从 0 开始的索引。<br />_________________________________________________________________________________________
</p>
<!-- mdladv_ignore_start --></div><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --></div><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --><div name = "Passed Check"  id = "Passed Check" class="PassedCheck" style="margin-left: 10pt;"><!-- mdladv_ignore_finish -->
<p><hr /></p>  <a name="CheckRecord_12"></a><div class="CheckHeader" id="Header_mathworks.codegen.LUTRangeCheckCode">
<!-- mdladv_ignore_start --><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAHTSURBVHgBrVO9ThtBEP7mLomDFEWXpEhFbD9BlC5SUGynSkdSpoiOMt2lovXdG0CqpEpIR2cXtPxJSNAgIyoaOKBBAuEzDRYSHmb29hYbRAWjk3a1O9/PzM4B9wy6dRIHAeBFBJpioJInUIfBHeAyQZyldxL4yatwwJgRYGAvGETEPGBLlDFxgubprMM4dPIyAtNv2T0dYid2e9L9mOw+o1FKsdzf0nPP2q4IOFZFHnLEziJRJXiDuDaNF6XnUo0/k5daEMBXcMCaidFQEgUvhi0069P4WJ3QlAADL3IEYu/tKIa5IFLwUtg2a+doG6vpWu7KRzjkgA3BXrSJ3WhT7JY1h6sG3BLwOLYE/GluEt1+TxSkUqaqI2BbatY/s4ot1MofaNEqK7gxN8lduS8aWti1JSDV9ev8d6TZQU4y1XbKjf9fRPmMhvsCMxfOAf0TVuxnh6pkSDSsMrLzHuNWUMeKw06fv2e6axo3jnp5Au2dBVPWtarzkAGDdzqV16+WBCGx/7d4SjYvQeZjvmGAvJ9oHs/mj1GETlZtrCeA9zDTODoSlJuQe/xA8+RPce6PMK+cr6NempfWBJr/7LH/+skjjy4uORWKX/IzfUPc3cBDxhXE6LNA6WFItQAAAABJRU5ErkJggg=="  />&#160;<!-- mdladv_ignore_finish --><span class="CheckHeading" id="Heading_mathworks.codegen.LUTRangeCheckCode">
标识哪些查找表模块会生成检查超范围值的高成本代码</span>
<!-- mdladv_ignore_start --><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --><!-- mdladv_ignore_finish --></div>
<!-- mdladv_ignore_start --><div class="subsection"><!-- mdladv_ignore_finish --><p /><p /><p>
<b>
<font color="Green">
通过 
</font>

</b>

</p>
<!-- mdladv_ignore_start --></div><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --></div><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --><div name = "Passed Check"  id = "Passed Check" class="PassedCheck" style="margin-left: 10pt;"><!-- mdladv_ignore_finish -->
<p><hr /></p>  <a name="CheckRecord_13"></a><div class="CheckHeader" id="Header_mathworks.codegen.LogicBlockUseNonBooleanOutput">
<!-- mdladv_ignore_start --><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAHTSURBVHgBrVO9ThtBEP7mLomDFEWXpEhFbD9BlC5SUGynSkdSpoiOMt2lovXdG0CqpEpIR2cXtPxJSNAgIyoaOKBBAuEzDRYSHmb29hYbRAWjk3a1O9/PzM4B9wy6dRIHAeBFBJpioJInUIfBHeAyQZyldxL4yatwwJgRYGAvGETEPGBLlDFxgubprMM4dPIyAtNv2T0dYid2e9L9mOw+o1FKsdzf0nPP2q4IOFZFHnLEziJRJXiDuDaNF6XnUo0/k5daEMBXcMCaidFQEgUvhi0069P4WJ3QlAADL3IEYu/tKIa5IFLwUtg2a+doG6vpWu7KRzjkgA3BXrSJ3WhT7JY1h6sG3BLwOLYE/GluEt1+TxSkUqaqI2BbatY/s4ot1MofaNEqK7gxN8lduS8aWti1JSDV9ev8d6TZQU4y1XbKjf9fRPmMhvsCMxfOAf0TVuxnh6pkSDSsMrLzHuNWUMeKw06fv2e6axo3jnp5Au2dBVPWtarzkAGDdzqV16+WBCGx/7d4SjYvQeZjvmGAvJ9oHs/mj1GETlZtrCeA9zDTODoSlJuQe/xA8+RPce6PMK+cr6NempfWBJr/7LH/+skjjy4uORWKX/IzfUPc3cBDxhXE6LNA6WFItQAAAABJRU5ErkJggg=="  />&#160;<!-- mdladv_ignore_finish --><span class="CheckHeading" id="Heading_mathworks.codegen.LogicBlockUseNonBooleanOutput">
检查逻辑模块的输出类型</span>
<!-- mdladv_ignore_start --><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --><!-- mdladv_ignore_finish --></div>
<!-- mdladv_ignore_start --><div class="subsection"><!-- mdladv_ignore_finish --><p /><p /><p>
标识输出非布尔数据类型的逻辑模块。<br /><br /><b>
<font color="Green">
通过 
</font>

</b>
<br />所有逻辑模块都得到适当的使用。
</p>
<!-- mdladv_ignore_start --></div><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --></div><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --><div name = "Failed Check" id = "Failed Check" class="FailedCheck" style="margin-left: 10pt;"> <!-- mdladv_ignore_finish -->
<p><hr /></p>  <a name="CheckRecord_562"></a><div class="CheckHeader" id="Header_mathworks.codegen.BlockSpecificQuestionableFxptOperations">
<!-- mdladv_ignore_start --><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAEDSURBVHgBrVIxCsJAENwVGzux1MZCxDKg+AYVLHxBYmXjI/IECxsr4wsshPgHEUkZxCJNLCWdnWf2SI41uYBBpzhud2fm9vYO4EcgD9x+18QXruJtvYAfCCHsiXfd5QwSsQNfIDaxUpMKF7emMxhdfOgsljkR5ahGHER0XKNrKoNYbGfJ3CQbSw3Kq0I1idu0hIc91JotrQHhtllLToI6N1Agku5Uyqc1jgpoQMTH+aRi2uvEhQZ0cmMwVDHtdYPVGvDWedu6QRI+ZkBPpBNzg+c95INUHUTcKCsuGGBAi/yJR6NnAYotlIHA+djzHfWVS5kkYmBXAJmIC2lrBYi4+C94A/a+eag5PUw2AAAAAElFTkSuQmCC"  />&#160;<!-- mdladv_ignore_finish --><span class="CheckHeading" id="Heading_mathworks.codegen.BlockSpecificQuestionableFxptOperations">
标识会生成高成本的定点和饱和代码的模块</span>
<!-- mdladv_ignore_start --><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --><!-- mdladv_ignore_finish --></div>
<!-- mdladv_ignore_start --><div class="subsection"><!-- mdladv_ignore_finish --><p /><p /><font color="#FF0000">Error occurred during model compile.<br /><br />"FixedStepDiscrete" 求解器不能用于仿真模块图 '<a href="matlab:open_system ('pipelined_TOP')">pipelined_TOP</a>'，因为它包含连续状态</font><!-- mdladv_ignore_start --></div><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --></div><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --><div name = "Failed Check" id = "Failed Check" class="FailedCheck" style="margin-left: 10pt;"> <!-- mdladv_ignore_finish -->
<p><hr /></p>  <a name="CheckRecord_505"></a><div class="CheckHeader" id="Header_mathworks.codegen.EnableLongLong">
<!-- mdladv_ignore_start --><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAEDSURBVHgBrVIxCsJAENwVGzux1MZCxDKg+AYVLHxBYmXjI/IECxsr4wsshPgHEUkZxCJNLCWdnWf2SI41uYBBpzhud2fm9vYO4EcgD9x+18QXruJtvYAfCCHsiXfd5QwSsQNfIDaxUpMKF7emMxhdfOgsljkR5ahGHER0XKNrKoNYbGfJ3CQbSw3Kq0I1idu0hIc91JotrQHhtllLToI6N1Agku5Uyqc1jgpoQMTH+aRi2uvEhQZ0cmMwVDHtdYPVGvDWedu6QRI+ZkBPpBNzg+c95INUHUTcKCsuGGBAi/yJR6NnAYotlIHA+djzHfWVS5kkYmBXAJmIC2lrBYi4+C94A/a+eag5PUw2AAAAAElFTkSuQmCC"  />&#160;<!-- mdladv_ignore_finish --><span class="CheckHeading" id="Heading_mathworks.codegen.EnableLongLong">
检查 'long long' 数据类型的用法</span>
<!-- mdladv_ignore_start --><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --><!-- mdladv_ignore_finish --></div>
<!-- mdladv_ignore_start --><div class="subsection"><!-- mdladv_ignore_finish --><p /><font color="#FF0000">Error occurred during model compile.<br /><br /></font><!-- mdladv_ignore_start --></div><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --></div><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --></div><!-- mdladv_ignore_finish -->
<span name = "EmbedImages" id="EmbedImages"></span><!-- mdladv_ignore_start -->
</div><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start -->
</div><!-- mdladv_ignore_finish -->
</body>  
</html>  
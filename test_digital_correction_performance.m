%% 数字校正性能测试脚本
% 专门测试修复后的数字校正模块性能

clear; clc; close all;

fprintf('=== 数字校正性能测试 ===\n');

%% 快速测试参数
params = struct();
params.Vref = 1.0;
params.num_stages = 8;
params.fs = 100e6;
params.f_in = 23/1024*100e6;
params.num_sine_cycles = 5;  % 减少到5个周期用于快速测试
params.num_cycles = 5;       % 添加缺失的num_cycles字段
params.resolution = 10;      % 添加分辨率参数
params.Vcm = 0;             % 添加共模电压参数

%% 生成测试数据
[t, sine_wave, clks, clkh] = generate_adaptive_timebase(params);
Vin_p = sine_wave;
Vin_n = zeros(size(sine_wave));

fprintf('测试参数: %d个周期, %.1f MHz输入, %d个样本\n', ...
        params.num_sine_cycles, params.f_in/1e6, length(t));

%% 运行主要处理
[final_output, binary_output, stage_history, timing_info] = ...
    event_driven_pipeline_adc_v3(Vin_p, Vin_n, params, [], t, clks, clkh);

%% 分析结果
final_valid = sum(abs(final_output) > 0.001);
binary_valid = sum(any(binary_output ~= 0, 2));
total_samples = length(t);

fprintf('\n=== 性能分析 ===\n');
fprintf('总样本数: %d\n', total_samples);
fprintf('最终DAC输出有效样本: %d (%.2f%%)\n', final_valid, 100*final_valid/total_samples);
fprintf('10bit二进制输出有效样本: %d (%.2f%%)\n', binary_valid, 100*binary_valid/total_samples);

if isfield(timing_info, 'correction_metrics')
    metrics = timing_info.correction_metrics;
    fprintf('数字校正成功率: %.2f%%\n', 100*metrics.correction_success_rate);
    fprintf('对齐效率: %.2f%%\n', 100*metrics.alignment_efficiency);
    fprintf('18bit重构效率: %.2f%%\n', 100*metrics.reconstruction_efficiency);
    fprintf('全加器效率: %.2f%%\n', 100*metrics.full_adder_efficiency);
    fprintf('DAC转换效率: %.2f%%\n', 100*metrics.dac_conversion_efficiency);
end

%% 显示有效输出的统计
if final_valid > 0
    valid_indices = find(abs(final_output) > 0.001);
    valid_values = final_output(valid_indices);
    
    fprintf('\n=== 有效输出统计 ===\n');
    fprintf('输出范围: [%.6f, %.6f] V\n', min(valid_values), max(valid_values));
    fprintf('平均值: %.6f V\n', mean(valid_values));
    fprintf('标准差: %.6f V\n', std(valid_values));
    
    % 显示前10个有效输出及其位置
    display_count = min(10, final_valid);
    fprintf('前%d个有效输出:\n', display_count);
    for i = 1:display_count
        idx = valid_indices(i);
        fprintf('  样本%d (t=%.6f μs): %.6f V\n', idx, t(idx)*1e6, valid_values(i));
    end
end

%% 显示各级处理统计
fprintf('\n=== 各级处理统计 ===\n');
sha_valid = sum(abs(stage_history.sha_data) > 0.001);
fprintf('SHA级有效输出: %d (%.2f%%)\n', sha_valid, 100*sha_valid/total_samples);

for stage = 1:params.num_stages
    analog_field = sprintf('stage_%d_analog', stage);
    if isfield(stage_history, analog_field)
        stage_valid = sum(abs(stage_history.(analog_field)) > 0.001);
        fprintf('STAGE%d有效输出: %d (%.2f%%)\n', stage, stage_valid, 100*stage_valid/total_samples);
    end
end

flash_valid = sum(any(stage_history.flash ~= 0, 2));
fprintf('Flash ADC有效输出: %d (%.2f%%)\n', flash_valid, 100*flash_valid/total_samples);

%% 性能评估
fprintf('\n=== 性能评估 ===\n');
if final_valid == 0
    fprintf('❌ 严重问题: 无任何有效最终输出\n');
elseif final_valid < total_samples * 0.001
    fprintf('⚠️  性能较低: 有效输出率 < 0.1%%\n');
elseif final_valid < total_samples * 0.01
    fprintf('⚠️  性能一般: 有效输出率 < 1%%\n');
else
    fprintf('✅ 性能良好: 有效输出率 ≥ 1%%\n');
end

fprintf('\n=== 数字校正性能测试完成 ===\n'); 
%% 事件驱动核心V5版本简化调试测试脚本
% 专门验证v5版本时序行为，只处理少量样本以观察详细过程

clear; clc; close all;

fprintf('=== 事件驱动核心V5版本简化调试测试 ===\n');
fprintf('目标：验证时序状态机行为，观察数据传播过程\n\n');

%% 1. 设置基本参数
params = struct();
params.Vref = 1.0;
params.num_stages = 8;
params.fs = 100e6;
params.resolution = 10;
params.Vcm = 0;
params.f_in = 23/1024*100e6;
params.num_sine_cycles = 1;  % 只处理1个周期进行调试

fprintf('基本参数设置完成\n');

%% 2. 生成简化的测试信号
try
    [t_sampled, sine_wave, clks_edges, clkh_edges] = generate_adaptive_timebase_v2(params);
    
    % 限制样本数量用于详细调试
    max_samples = 200;  % 只处理前200个样本
    if length(t_sampled) > max_samples
        t_sampled = t_sampled(1:max_samples);
        sine_wave = sine_wave(1:max_samples);
        clks_edges.rising = clks_edges.rising(1:max_samples);
        clks_edges.falling = clks_edges.falling(1:max_samples);
        clkh_edges.rising = clkh_edges.rising(1:max_samples);
        clkh_edges.falling = clkh_edges.falling(1:max_samples);
    end
    
    fprintf('测试信号生成成功: %d个样本\n', length(t_sampled));
    fprintf('边沿事件统计:\n');
    fprintf('  CLKS上升沿: %d, 下降沿: %d\n', sum(clks_edges.rising), sum(clks_edges.falling));
    fprintf('  CLKH上升沿: %d, 下降沿: %d\n', sum(clkh_edges.rising), sum(clkh_edges.falling));
    
catch ME
    fprintf('测试信号生成失败: %s\n', ME.message);
    return;
end

%% 3. 调用事件驱动核心V5版本进行详细调试
fprintf('\n开始调用事件驱动核心V5版本 (调试模式)...\n');
fprintf('============================================\n');

try
    % 创建差分输入信号
    Vin_p = sine_wave;
    Vin_n = zeros(size(sine_wave));
    
    % 调用V5版本核心 (已启用调试模式)
    [adc_output, binary_output, stage_history, timing_info] = ...
        event_processing_core_v5(Vin_p, Vin_n, t_sampled, clks_edges, clkh_edges, ...
                                [], [], params);
    
    fprintf('============================================\n');
    fprintf('V5版本调用成功！\n\n');
    
catch ME
    fprintf('V5版本调用失败: %s\n', ME.message);
    fprintf('错误位置: %s\n', ME.stack(1).name);
    return;
end

%% 4. 分析结果
fprintf('=== V5版本详细结果分析 ===\n');
fprintf('处理事件总数: %d\n', timing_info.processed_events);
fprintf('有效输出数量: %d\n', timing_info.valid_outputs);
fprintf('处理效率: %.2f%%\n', 100*timing_info.processing_efficiency);
fprintf('数据传播次数: %d\n', timing_info.data_propagations);

% 检查各级输出状态
sha_valid = sum(abs(stage_history.sha_data) > 0.001);
stage1_valid = sum(abs(stage_history.stage_1_analog) > 0.001);
final_valid = sum(abs(adc_output) > 0.001);

fprintf('\n级别输出状态:\n');
fprintf('  SHA级有效输出: %d/%d\n', sha_valid, length(t_sampled));
fprintf('  STAGE1有效输出: %d/%d\n', stage1_valid, length(t_sampled));
fprintf('  最终有效输出: %d/%d\n', final_valid, length(t_sampled));

%% 5. 时序验证分析
fprintf('\n=== 时序验证分析 ===\n');

% 查找第一个有效的SHA输出
sha_first_valid = find(abs(stage_history.sha_data) > 0.001, 1);
if ~isempty(sha_first_valid)
    fprintf('第一个有效SHA输出: 样本%d, 值=%.3f\n', sha_first_valid, stage_history.sha_data(sha_first_valid));
end

% 查找第一个有效的STAGE1输出
stage1_first_valid = find(abs(stage_history.stage_1_analog) > 0.001, 1);
if ~isempty(stage1_first_valid)
    fprintf('第一个有效STAGE1输出: 样本%d, 值=%.3f\n', stage1_first_valid, stage_history.stage_1_analog(stage1_first_valid));
else
    fprintf('STAGE1无有效输出 - 数据传播问题\n');
end

%% 6. 边沿事件分析
fprintf('\n=== 边沿事件时序分析 ===\n');

% 找到前几个边沿事件
clks_rising_indices = find(clks_edges.rising, 5);
clks_falling_indices = find(clks_edges.falling, 5);
clkh_rising_indices = find(clkh_edges.rising, 5);
clkh_falling_indices = find(clkh_edges.falling, 5);

fprintf('前5个CLKS上升沿时间: [%.3f %.3f %.3f %.3f %.3f] ns\n', t_sampled(clks_rising_indices)*1e9);
fprintf('前5个CLKS下降沿时间: [%.3f %.3f %.3f %.3f %.3f] ns\n', t_sampled(clks_falling_indices)*1e9);
fprintf('前5个CLKH上升沿时间: [%.3f %.3f %.3f %.3f %.3f] ns\n', t_sampled(clkh_rising_indices)*1e9);
fprintf('前5个CLKH下降沿时间: [%.3f %.3f %.3f %.3f %.3f] ns\n', t_sampled(clkh_falling_indices)*1e9);

%% 7. 给出调试建议
fprintf('\n=== 调试建议 ===\n');
if timing_info.data_propagations == 0
    fprintf('! 关键问题：数据传播次数为0\n');
    fprintf('  建议检查：\n');
    fprintf('  1. SHA到STAGE1的状态检查逻辑\n');
    fprintf('  2. 时钟边沿的时序匹配\n');
    fprintf('  3. 状态转换的执行顺序\n');
end

if sha_valid > 0 && stage1_valid == 0
    fprintf('! SHA工作正常但STAGE1无输出\n');
    fprintf('  可能原因：STAGE1数据接收时序不正确\n');
end

fprintf('\nV5版本简化调试测试完成\n'); 
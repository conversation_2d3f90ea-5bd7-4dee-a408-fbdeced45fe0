function [digital_data_struct, analog_data_struct, processing_info] = ...
    event_processing_core_v6(Vin_p, Vin_n, t, clks, clkh, ...
                            digital_data_struct, analog_data_struct, ...
                            rel_error_params, params)
%EVENT_PROCESSING_CORE_V6 事件驱动处理核心 (v6版本) - 规范化时钟事件处理架构
%   VERSION HISTORY:
%   v1-v5: 复杂的有效性标记、握手信号、标签对齐机制
%   v6: 回归实际电路设计思路的完全重构版本
%   v6.1 (当前): 规范化时钟事件处理机制的进一步优化
%
%   v6.1版本核心改进:
%   1. 规范化时钟事件命名: CLKS_rising/falling/high, CLKH_rising/falling/high
%   2. 分层处理架构: 核心层面使用通用时钟标识，模块层面决定具体状态
%   3. 增强的状态分类: 支持边沿事件、电平状态、空闲状态、错误检测
%   4. 错误检测机制: 识别非交叠时钟违规情况并统计
%   5. 简化状态判断逻辑: 移除复杂的优先级判断，提高代码可读性
%   6. 统一接口设计: 保持与现有模块的兼容性
%   7. 完善的统计报告: 包含时钟完整性检验
%   8. 时钟分配策略保持不变：
%      - SHA、STAGE2、4、6、8：采样态受CLKH控制，保持态受CLKS控制
%      - STAGE1、3、5、7、Flash ADC：采样态受CLKS控制，保持态受CLKH控制
%
%   输入参数:
%       Vin_p, Vin_n - 差分输入信号
%       t - 时间向量
%       clks, clkh - 非交叠时钟信号
%       digital_data_struct - 数字数据结构体 (18位独立存储)
%       analog_data_struct - 模拟数据结构体 (9条线路独立存储)
%       rel_error_params - 相对误差参数
%       params - ADC参数
%
%   输出参数:
%       digital_data_struct - 填充后的数字数据结构体
%       analog_data_struct - 填充后的模拟数据结构体
%       processing_info - 处理统计信息

    fprintf('=== 事件驱动处理核心 v6.0 (回归实际电路设计) ===\n');
    
    %% 初始化处理参数
    num_samples = length(t);
    num_stages = params.num_stages;
    
    % 初始化处理统计
    processing_info = struct();
    processing_info.version = 'v6.0_circuit_design';
    processing_info.total_samples = num_samples;
    processing_info.processed_samples = 0;
    processing_info.sampling_events = 0;
    processing_info.holding_events = 0;
    processing_info.idle_events = 0;
    processing_info.digital_outputs = 0;
    processing_info.analog_outputs = 0;
    
    % 计算差分输入
    Vin_diff = (Vin_p - Vin_n) / 2;
    
    %% 初始化流水线状态机（v6版本：简化无握手机制）
    pipeline_state = initialize_pipeline(num_stages, params);
    
    fprintf('初始化完成: %d级流水线, %d个时间点\n', num_stages, num_samples);
    fprintf('时钟分配: SHA/偶数级->CLKH采样+CLKS保持, 奇数级/Flash->CLKS采样+CLKH保持\n');
    
    %% 主要事件驱动循环 - 基于非交叠时钟的四种时钟事件
    % 非交叠时钟的存在使得控制时钟不会在同一时间节点发生变化，在模型中完全避免数据竞争
    fprintf('开始v6事件驱动处理...\n');
    
    for time_idx = 1:num_samples
        current_input = Vin_diff(time_idx);
        current_clks = clks(time_idx);
        current_clkh = clkh(time_idx);
        
        % 确定全局时钟事件类型
        clock_event = determine_clock_event(current_clks, current_clkh, time_idx, clks, clkh);
        
        % 处理SHA模块 (CLKH采样，CLKS保持)
        [pipeline_state.sha, analog_data_struct] = ...
            process_sha_module(pipeline_state.sha, current_input, clock_event, ...
                              time_idx, analog_data_struct);
        
        % 处理所有流水线级
        for stage = 1:num_stages
            [pipeline_state.stages{stage}, analog_data_struct, digital_data_struct] = ...
                process_pipeline_stage(pipeline_state.stages{stage}, stage, ...
                                      pipeline_state, clock_event, time_idx, ...
                                      rel_error_params, params, ...
                                      analog_data_struct, digital_data_struct);
        end
        
        % 处理Flash ADC (CLKS采样，CLKH保持)
        [pipeline_state.flash, digital_data_struct] = ...
            process_flash_adc(pipeline_state.flash, pipeline_state.stages{end}, ...
                             clock_event, time_idx, params, ...
                             digital_data_struct);
        
        % 更新处理统计（基于新的规范化事件类型）
        processing_info.processed_samples = processing_info.processed_samples + 1;
        switch clock_event.type
            case {'CLKS_rising', 'CLKS_falling', 'CLKS_high'}
                processing_info.sampling_events = processing_info.sampling_events + 1;
            case {'CLKH_rising', 'CLKH_falling', 'CLKH_high'}
                processing_info.holding_events = processing_info.holding_events + 1;
            case 'IDLE'
                processing_info.idle_events = processing_info.idle_events + 1;
            case 'ERROR_OVERLAP'
                % 添加错误事件统计
                if ~isfield(processing_info, 'error_events')
                    processing_info.error_events = 0;
                end
                processing_info.error_events = processing_info.error_events + 1;
        end
        
        % 简化的进度显示
        if mod(time_idx, 100000) == 0
            fprintf('进度: %d/%d时间点 (%.1f%%)\n', time_idx, num_samples, 100*time_idx/num_samples);
        end
    end
    
    %% 统计最终结果
    % 统计数字信号输出
    digital_outputs = 0;
    for i = 1:length(digital_data_struct.signals)
        digital_outputs = digital_outputs + sum(digital_data_struct.signals(i).values ~= 0);
    end
    processing_info.digital_outputs = digital_outputs;

    % 统计模拟信号输出
    analog_outputs = 0;
    for i = 1:length(analog_data_struct.signals)
        analog_outputs = analog_outputs + sum(analog_data_struct.signals(i).values ~= 0);
    end
    processing_info.analog_outputs = analog_outputs;

    processing_info.output_efficiency = processing_info.digital_outputs / num_samples;
    
    fprintf('v6事件驱动处理完成 (规范化时钟事件处理):\n');
    fprintf('  处理样本: %d/%d\n', processing_info.processed_samples, num_samples);
    fprintf('  数字输出: %d时间点 (%.1f%%)\n', processing_info.digital_outputs, 100*processing_info.output_efficiency);
    fprintf('  模拟输出: %d时间点\n', processing_info.analog_outputs);
    
    % 事件统计（包含错误检测）
    if isfield(processing_info, 'error_events') && processing_info.error_events > 0
        fprintf('  事件统计: CLKS事件%d, CLKH事件%d, 空闲%d, 错误%d\n', ...
                processing_info.sampling_events, processing_info.holding_events, ...
                processing_info.idle_events, processing_info.error_events);
        fprintf('  警告: 检测到%d个非交叠时钟违规事件\n', processing_info.error_events);
    else
        fprintf('  事件统计: CLKS事件%d, CLKH事件%d, 空闲%d\n', ...
                processing_info.sampling_events, processing_info.holding_events, processing_info.idle_events);
        fprintf('  时钟完整性: 通过 (无非交叠违规)\n');
    end
end

function pipeline_state = initialize_pipeline(num_stages, params)
%INITIALIZE_PIPELINE 初始化流水线状态机

    pipeline_state = struct();
    
    % SHA状态初始化 (CLKH采样，CLKS保持)
    pipeline_state.sha = struct(...
        'state', 'IDLE', ...
        'input_voltage', 0, ...
        'output_voltage', 0, ...
        'sampling_clock', 'CLKH', ...
        'holding_clock', 'CLKS', ...
        'last_time_index', 0);
    
    % 流水线级状态初始化
    pipeline_state.stages = cell(num_stages, 1);
    for stage = 1:num_stages
        % 时钟分配：奇数级CLKS采样+CLKH保持，偶数级CLKH采样+CLKS保持
        if mod(stage, 2) == 1
            sampling_clock = 'CLKS';
            holding_clock = 'CLKH';
        else
            sampling_clock = 'CLKH';
            holding_clock = 'CLKS';
        end
        
        pipeline_state.stages{stage} = struct(...
            'stage_id', stage, ...
            'state', 'IDLE', ...
            'input_voltage', 0, ...
            'output_analog_voltage', 0, ...
            'output_digital_bits', [0, 0], ...
            'sampling_clock', sampling_clock, ...
            'holding_clock', holding_clock, ...
            'last_time_index', 0);
    end
    
    % Flash ADC状态初始化 (CLKS采样，CLKH保持)
    pipeline_state.flash = struct(...
        'state', 'IDLE', ...
        'input_voltage', 0, ...
        'output_digital_bits', [0, 0], ...
        'sampling_clock', 'CLKS', ...
        'holding_clock', 'CLKH', ...
        'last_time_index', 0);
    
    fprintf('流水线状态机初始化完成\n');
end

function clock_event = determine_clock_event(current_clks, current_clkh, time_idx, clks, clkh)
%DETERMINE_CLOCK_EVENT 确定当前的通用时钟事件类型

    clock_event = struct();
    clock_event.clks = current_clks;
    clock_event.clkh = current_clkh;
    clock_event.time_index = time_idx;
    
    % 检查时钟边沿（优先处理边沿事件）
    if time_idx > 1
        prev_clks = clks(time_idx - 1);
        prev_clkh = clkh(time_idx - 1);
        
        clks_rising = (prev_clks == 0) && (current_clks == 1);
        clks_falling = (prev_clks == 1) && (current_clks == 0);
        clkh_rising = (prev_clkh == 0) && (current_clkh == 1);
        clkh_falling = (prev_clkh == 1) && (current_clkh == 0);
        
        % 优先处理时钟边沿事件（使用规范化命名）
        if clks_rising
            clock_event.type = 'CLKS_rising';
            clock_event.primary_clock = 'CLKS';
            clock_event.system_state = 'Transition';
        elseif clks_falling
            clock_event.type = 'CLKS_falling';
            clock_event.primary_clock = 'CLKS';
            clock_event.system_state = 'Transition';
        elseif clkh_rising
            clock_event.type = 'CLKH_rising';
            clock_event.primary_clock = 'CLKH';
            clock_event.system_state = 'Transition';
        elseif clkh_falling
            clock_event.type = 'CLKH_falling';
            clock_event.primary_clock = 'CLKH';
            clock_event.system_state = 'Transition';
        else
            % 无边沿变化，确定电平状态
            if current_clks == 1 && current_clkh == 0
                % CLKS活跃，符合非交叠原则
                clock_event.type = 'CLKS_high';
                clock_event.primary_clock = 'CLKS';
                clock_event.system_state = 'Active';
            elseif current_clks == 0 && current_clkh == 1
                % CLKH活跃，符合非交叠原则
                clock_event.type = 'CLKH_high';
                clock_event.primary_clock = 'CLKH';
                clock_event.system_state = 'Active';
            elseif current_clks == 0 && current_clkh == 0
                % 两时钟均为低，正常空闲状态
                clock_event.type = 'IDLE';
                clock_event.primary_clock = 'NONE';
                clock_event.system_state = 'IDLE';
            else
                % 异常状态：两时钟同时为高（违反非交叠原则）
                clock_event.type = 'ERROR_OVERLAP';
                clock_event.primary_clock = 'ERROR';
                clock_event.system_state = 'ERROR';
                % 可选：在此处添加警告信息
                warning('检测到非交叠时钟违规：CLKS=%d, CLKH=%d', current_clks, current_clkh);
            end
        end
    else
        % 初始时刻，直接检测电平状态
        if current_clks == 1 && current_clkh == 0
            % CLKS活跃，符合非交叠原则
            clock_event.type = 'CLKS_high';
            clock_event.primary_clock = 'CLKS';
            clock_event.system_state = 'Active';
        elseif current_clks == 0 && current_clkh == 1
            % CLKH活跃，符合非交叠原则
            clock_event.type = 'CLKH_high';
            clock_event.primary_clock = 'CLKH';
            clock_event.system_state = 'Active';
        elseif current_clks == 0 && current_clkh == 0
            % 两时钟均为低，正常空闲状态
            clock_event.type = 'IDLE';
            clock_event.primary_clock = 'NONE';
            clock_event.system_state = 'IDLE';
        else
            % 异常状态：两时钟同时为高（违反非交叠原则）
            clock_event.type = 'ERROR_OVERLAP';
            clock_event.primary_clock = 'ERROR';
            clock_event.system_state = 'ERROR';
            % 可选：在此处添加警告信息
            warning('检测到非交叠时钟违规：CLKS=%d, CLKH=%d', current_clks, current_clkh);
        end
    end
end

function [sha_state, analog_data_struct] = ...
    process_sha_module(sha_state, input_voltage, clock_event, ...
                      time_idx, analog_data_struct)
%PROCESS_SHA_MODULE 处理SHA模块

    % SHA时钟配置：CLKH采样，CLKS保持
    new_state = determine_module_state(sha_state, clock_event);
    
    % 状态转移和数据处理
    switch new_state
        case 'Sampling'
            % 采样态：接收/跟踪输入数据，输出为0
            sha_state.input_voltage = input_voltage;
            sha_state.output_voltage = 0;
            sha_state.state = 'Sampling';
            
        case 'Holding'
            % 保持态：输出锁存的数据
            sha_state.output_voltage = sha_state.input_voltage;
            sha_state.state = 'Holding';
            
        case 'IDLE'
            % 空闲态：输出为0
            sha_state.output_voltage = 0;
            sha_state.state = 'IDLE';
    end
    
    sha_state.last_time_index = time_idx;

    % 存储到模拟数据结构体（SHA为第1个信号）
    analog_data_struct.signals(1).values(time_idx) = sha_state.output_voltage;
end

function [stage_state, analog_data_struct, digital_data_struct] = ...
    process_pipeline_stage(stage_state, stage_id, pipeline_state, clock_event, ...
                          time_idx, rel_error_params, params, ...
                          analog_data_struct, digital_data_struct)
%PROCESS_PIPELINE_STAGE 处理流水线级

    % 获取前级数据
    if stage_id == 1
        prev_output = pipeline_state.sha.output_voltage;
    else
        prev_output = pipeline_state.stages{stage_id - 1}.output_analog_voltage;
    end
    
    % 确定当前级的状态
    new_state = determine_module_state(stage_state, clock_event);
    
    % 状态转移和数据处理
    switch new_state
        case 'Sampling'
            % 采样态：接收前级数据，输出为0
            stage_state.input_voltage = prev_output;
            stage_state.output_analog_voltage = 0;
            stage_state.output_digital_bits = [0, 0];
            stage_state.state = 'Sampling';
            
        case 'Holding'
            % 保持态：处理数据并输出
            [analog_out, digital_out] = process_1p5bit_stage(...
                stage_state.input_voltage, stage_id, rel_error_params, params);
            
            stage_state.output_analog_voltage = analog_out;
            stage_state.output_digital_bits = digital_out;
            stage_state.state = 'Holding';
            
        case 'IDLE'
            % 空闲态：输出为0
            stage_state.output_analog_voltage = 0;
            stage_state.output_digital_bits = [0, 0];
            stage_state.state = 'IDLE';
    end
    
    stage_state.last_time_index = time_idx;
    
    % 存储到数据结构体
    % 模拟数据结构体（第stage_id+1个信号，因为第1个是SHA）
    analog_data_struct.signals(stage_id + 1).values(time_idx) = stage_state.output_analog_voltage;

    % 数字数据结构体（每级2bit）
    bit_start = (stage_id - 1) * 2 + 1;
    digital_data_struct.signals(bit_start).values(time_idx) = stage_state.output_digital_bits(1);
    digital_data_struct.signals(bit_start + 1).values(time_idx) = stage_state.output_digital_bits(2);
end

function [flash_state, digital_data_struct] = ...
    process_flash_adc(flash_state, last_stage_state, clock_event, ...
                     time_idx, params, digital_data_struct)
%PROCESS_FLASH_ADC 处理Flash ADC

    % 获取前级数据
    prev_output = last_stage_state.output_analog_voltage;
    
    % 确定Flash ADC状态
    new_state = determine_module_state(flash_state, clock_event);
    
    % 状态转移和数据处理
    switch new_state
        case 'Sampling'
            % 采样态：接收前级数据
            flash_state.input_voltage = prev_output;
            flash_state.output_digital_bits = [0, 0];
            flash_state.state = 'Sampling';
            
        case 'Holding'
            % 保持态：执行2bit Flash ADC量化
            flash_digital = process_2bit_flash_adc(flash_state.input_voltage, params);
            flash_state.output_digital_bits = flash_digital;
            flash_state.state = 'Holding';
            
        case 'IDLE'
            % 空闲态：输出为0
            flash_state.output_digital_bits = [0, 0];
            flash_state.state = 'IDLE';
    end
    
    flash_state.last_time_index = time_idx;

    % 存储到数字数据结构体（最后2bit：第17和18个信号）
    digital_data_struct.signals(17).values(time_idx) = flash_state.output_digital_bits(1);
    digital_data_struct.signals(18).values(time_idx) = flash_state.output_digital_bits(2);
end

function new_state = determine_module_state(module_state, clock_event)
%DETERMINE_MODULE_STATE 根据时钟沿事件确定模块状态 (v6.1改进版)
%   v6.1 改进内容:
%   1. 基于时钟沿事件的精确状态跳转逻辑
%   2. 上升沿触发状态进入，下降沿触发状态退出
%   3. 高电平期间维持当前状态，确保时序精确性
%   4. 增强的模块时钟配置匹配机制
%   5. 保持原有的错误处理和空闲状态逻辑

    % 处理系统级错误状态
    if strcmp(clock_event.system_state, 'ERROR')
        new_state = 'IDLE';  % 错误状态下强制进入空闲态
        return;
    end
    
    % 处理系统空闲状态
    if strcmp(clock_event.type, 'IDLE')
        new_state = 'IDLE';
        return;
    end
    
    % 基于具体时钟事件类型的精确状态判断
    % 核心改进：区分边沿事件和电平状态，实现精确的时序控制
    switch clock_event.type
        case 'CLKS_rising'
            % CLKS上升沿：检查模块是否响应CLKS上升沿
            if strcmp(module_state.sampling_clock, 'CLKS')
                new_state = 'Sampling';  % 进入采样态
            elseif strcmp(module_state.holding_clock, 'CLKS')
                new_state = 'Holding';   % 进入保持态
            else
                new_state = 'IDLE';      % 与该模块无关
            end
            
        case 'CLKS_high'
            % CLKS高电平期间：维持当前状态
            if strcmp(module_state.sampling_clock, 'CLKS')
                new_state = 'Sampling';  % 维持采样态
            elseif strcmp(module_state.holding_clock, 'CLKS')
                new_state = 'Holding';   % 维持保持态
            else
                new_state = 'IDLE';      % 维持空闲态
            end
            
        case 'CLKS_falling'
            % CLKS下降沿：无论之前是什么状态，都退出到空闲态
            % 这确保了精确的时钟控制时序
            new_state = 'IDLE';
            
        case 'CLKH_rising'
            % CLKH上升沿：检查模块是否响应CLKH上升沿
            if strcmp(module_state.sampling_clock, 'CLKH')
                new_state = 'Sampling';  % 进入采样态
            elseif strcmp(module_state.holding_clock, 'CLKH')
                new_state = 'Holding';   % 进入保持态
            else
                new_state = 'IDLE';      % 与该模块无关
            end
            
        case 'CLKH_high'
            % CLKH高电平期间：维持当前状态
            if strcmp(module_state.sampling_clock, 'CLKH')
                new_state = 'Sampling';  % 维持采样态
            elseif strcmp(module_state.holding_clock, 'CLKH')
                new_state = 'Holding';   % 维持保持态
            else
                new_state = 'IDLE';      % 维持空闲态
            end
            
        case 'CLKH_falling'
            % CLKH下降沿：无论之前是什么状态，都退出到空闲态
            % 这确保了精确的时钟控制时序
            new_state = 'IDLE';
            
        otherwise
            % 未知时钟事件类型，默认为空闲
            new_state = 'IDLE';
    end
end

function [analog_out, digital_out] = process_1p5bit_stage(input_voltage, stage_id, rel_error_params, params)
%PROCESS_1P5BIT_STAGE 处理1.5bit流水线级
%   使用正确的MDAC残差输出公式：
%   Vres = (1+gain_error)*(2+cap_mismatch)*Vin - (1+gain_error)*(1+cap_mismatch)*D*Vref + offset_error
%   
%   参数说明：
%   gain_error: 第i级的独立增益误差
%   cap_mismatch: 第i级的独立电容失配误差
%   D: 数字输出码对应的数值（+1, 0, -1）
%
%   rel_error_params参数结构：
%   8×3矩阵：[gain_error, cap_mismatch, offset_error]
%   注意：参数格式验证由上层函数event_driven_pipeline_adc_v4完成

    % 获取误差参数（由上层函数event_driven_pipeline_adc_v4已验证格式）
    gain_error = rel_error_params(stage_id, 1);      % 增益误差
    cap_mismatch = rel_error_params(stage_id, 2);    % 电容失配误差
    offset_error = rel_error_params(stage_id, 3);    % 偏移误差
    
    % 1.5bit ADC量化
    Vref = params.Vref;
    threshold_high = Vref/4;
    threshold_low = -Vref/4;
    
    % 执行量化并确定数字输出码D值
    if input_voltage > threshold_high
        digital_code = [1, 0];  % 二进制编码
        D = 1;                  % 对应的数值：+1
    elseif input_voltage < threshold_low
        digital_code = [0, 0];  % 二进制编码
        D = -1;                 % 对应的数值：-1
    else
        digital_code = [0, 1];  % 二进制编码
        D = 0;                  % 对应的数值：0
    end
    
    % MDAC残差输出计算公式
    % Vres = (1+gain_error)*(2+cap_mismatch)*Vin - (1+gain_error)*(1+cap_mismatch)*D*Vref + offset_error
    analog_residue = (1 + gain_error) * (2 + cap_mismatch) * input_voltage - ...
                    (1 + gain_error) * (1 + cap_mismatch) * D * Vref + offset_error;
    
    % 限幅
    analog_out = max(-Vref, min(Vref, analog_residue));
    digital_out = digital_code;
end

function flash_digital = process_2bit_flash_adc(input_voltage, params)
%PROCESS_2BIT_FLASH_ADC 处理2bit Flash ADC (标准2-bit ADC阈值)
%   标准2-bit ADC量化阈值：[-Vref/2, 0, Vref/2]
%   量化区间：
%   00: input_voltage < -Vref/2
%   01: -Vref/2 ≤ input_voltage < 0  
%   10: 0 ≤ input_voltage < Vref/2
%   11: input_voltage ≥ Vref/2

    Vref = params.Vref;
    % 标准2-bit ADC阈值：[-1/2Vref, 0, 1/2Vref]
    thresholds = [-Vref/2, 0, Vref/2];
    
    if input_voltage >= thresholds(3)
        flash_digital = [1, 1];  % 11: [Vref/2, +∞)
    elseif input_voltage >= thresholds(2)
        flash_digital = [1, 0];  % 10: [0, Vref/2)
    elseif input_voltage >= thresholds(1)
        flash_digital = [0, 1];  % 01: [-Vref/2, 0)
    else
        flash_digital = [0, 0];  % 00: (-∞, -Vref/2)
    end
end 
function [adc_output, binary_output, stage_history, timing_info] = event_driven_pipeline_adc_v3(Vin_p, Vin_n, params, rel_error_params, t_sampled, clks, clkh)
%EVENT_DRIVEN_PIPELINE_ADC_V3 事件驱动流水线ADC主模型 (v3版本) - 重构优化版本
%   VERSION HISTORY:
%   v1: 原始版本 - 基础流水线ADC模型
%   v2: 专注理想流水线ADC的零误差输出正确性验证
%   v3: 重构优化版本 - 按照改进规范完全重写
%
%   v3重构版本主要特点:
%   1. 使用原版时钟生成器 generate_adaptive_timebase.m
%   2. 集成重构的event_processing_core_v5
%   3. 周期级标签分配系统
%   4. 三状态流水线状态机 (sampling/holding/idle)
%   5. 级间握手协议 (valid信号)
%   6. 严格非交叠时钟控制架构
%
%   技术规范要求:
%   1. 输入信号: 频率=23/1024*100e6 Hz，30个周期，相干采样
%   2. 模型输出: 两种模拟输出 + 理想ADC对比基准
%   3. 时序控制: 原版时钟信号 (0/1电平)
%   4. SHA电路: CLKH采样，CLKS保持输出
%   5. 1.5bit流水线级: 阈值[-1/4Vref, 1/4Vref]，传递函数实现
%   6. Flash ADC: 2bit完整量化，CLKS采样，CLKH保持
%   7. 数字校准: 周期级标签化18bit→10bit校正，理想DAC转换
%
%   输入参数:
%       Vin_p, Vin_n - 差分输入信号
%       params - ADC参数结构体
%       rel_error_params - 相对误差参数矩阵
%       t_sampled - 时间向量
%       clks, clkh - 原版时钟信号 (0/1电平)
%
%   输出参数:
%       adc_output - 最终DAC转换的模拟信号输出(模拟输出2)
%       binary_output - 10bit有效数字输出
%       stage_history - 各级历史数据(包含周期标签信息)
%       timing_info - V3重构版本时序信息和性能统计
%
%   版本: v3.0_restructured - 按照改进规范完全重构的优化版本

    fprintf('=== 事件驱动流水线ADC v3.0 (重构优化版本) ===\n');
    
    % 参数验证和初始化
    [Vin_p, Vin_n, t_sampled, clks, clkh] = validate_inputs_v3_restructured(...
        Vin_p, Vin_n, t_sampled, clks, clkh);

    num_samples = length(t_sampled);
    num_stages = params.num_stages;

    if nargin < 4 || isempty(rel_error_params)
        % 当前测试目标为理想条件（零误差）
        rel_error_params = zeros(num_stages, 2);
        fprintf('使用理想条件：零误差参数 (%dx%d)\n', size(rel_error_params));
    end

    fprintf('流水线配置: %.1fMHz, %d个周期, %d级\n', ...
            params.f_in/1e6, params.num_cycles, num_stages);

    % 验证输入频率
    expected_freq = 23/1024*100e6;
    if abs(params.f_in - expected_freq) > 1e3
        warning('输入频率偏离相干采样要求');
    end

    % 初始化时序控制系统
    timing_control = initialize_timing_control_system_v3_restructured(num_stages, params);

    % 验证原版时钟信号格式
    validate_original_clock_signals_v3(clks, clkh, t_sampled);

    fprintf('开始v3事件驱动处理...\n');

    % 调用重构的事件驱动核心V5
    [adc_output, binary_output, stage_history, timing_info] = ...
        event_processing_core_v5(Vin_p, Vin_n, t_sampled, clks, clkh, ...
                                timing_control, rel_error_params, params);

    % 后处理和性能统计
    [adc_output, binary_output, stage_history, performance_metrics] = ...
        finalize_pipeline_processing_v3_restructured(adc_output, binary_output, stage_history, params);

    % 更新时序信息
    timing_info.performance_metrics = performance_metrics;
    timing_info.model_version = 'v3.0_restructured';
    timing_info.integration_modules = {'event_core_v5_restructured', 'digital_correction_v3', 'timebase_original'};

    fprintf('事件驱动流水线ADC v3.0处理完成\n');
    fprintf('有效输出样本: %d/%d (%.1f%%)\n', ...
            sum(abs(adc_output) > 0.001), num_samples, ...
            100*sum(abs(adc_output) > 0.001)/num_samples);
end

function timing_control = initialize_timing_control_system_v3_restructured(num_stages, params)
%INITIALIZE_TIMING_CONTROL_SYSTEM_V3_RESTRUCTURED 初始化时序控制系统 (v3重构版本)

    timing_control = struct();

    % V3重构版本：原版时钟分配方案
    % SHA:      采样态(CLKH=1) → 保持态(CLKS=1)
    % STAGE1:   采样态(CLKS=1) → 保持态(CLKH=1)
    % STAGE2:   采样态(CLKH=1) → 保持态(CLKS=1)
    % ...
    % Flash ADC: 采样态(CLKS=1) → 保持态(CLKH=1)

    timing_control.clock_assignment = struct();
    timing_control.clock_assignment.sha = struct(...
        'sample_clock', 'clkh', 'hold_clock', 'clks');

    for stage = 1:num_stages
        if mod(stage, 2) == 1  % 奇数级 (STAGE1,3,5,7)
            timing_control.clock_assignment.(['stage_', num2str(stage)]) = struct(...
                'sample_clock', 'clks', 'hold_clock', 'clkh');
        else  % 偶数级 (STAGE2,4,6,8)
            timing_control.clock_assignment.(['stage_', num2str(stage)]) = struct(...
                'sample_clock', 'clkh', 'hold_clock', 'clks');
        end
    end

    timing_control.clock_assignment.flash = struct(...
        'sample_clock', 'clks', 'hold_clock', 'clkh');

    % V3重构版本：周期级标签管理配置
    timing_control.tag_config = struct();
    timing_control.tag_config.enable_cycle_tags = true;        % 启用周期级标签
    timing_control.tag_config.sampling_frequency = params.fs; % 采样频率
    timing_control.tag_config.cycle_based_assignment = true;   % 基于周期的标签分配

    % V3重构版本：三状态机配置
    timing_control.state_machine_config = struct();
    timing_control.state_machine_config.enable_three_states = true;  % 启用三状态机
    timing_control.state_machine_config.valid_signal_control = true; % 启用valid信号控制
    timing_control.state_machine_config.handshake_protocol = true;   % 启用握手协议

    fprintf('时序控制系统v3重构版本初始化完成: 支持原版时钟和周期标签\n');
end

function validate_original_clock_signals_v3(clks, clkh, t_sampled)
%VALIDATE_ORIGINAL_CLOCK_SIGNALS_V3 验证原版时钟信号格式

    % 检查信号长度
    if length(clks) ~= length(t_sampled) || length(clkh) ~= length(t_sampled)
        error('时钟信号长度必须与时间向量长度一致');
    end

    % 检查信号范围 (原版时钟应该是0/1)
    if any(clks ~= 0 & clks ~= 1) || any(clkh ~= 0 & clkh ~= 1)
        error('原版时钟信号必须是0/1逻辑电平');
    end

    % 统计时钟活跃时间
    clks_high_count = sum(clks == 1);
    clkh_high_count = sum(clkh == 1);

    fprintf('时钟信号验证: CLKS %.1f%%, CLKH %.1f%%\n', ...
            100*clks_high_count/length(clks), 100*clkh_high_count/length(clkh));

    % 验证非交叠特性
    if sum((clks == 1) & (clkh == 1)) > 0
        warning('检测到时钟重叠');
    end
end

function [Vin_p, Vin_n, t_sampled, clks, clkh] = validate_inputs_v3_restructured(...
    Vin_p, Vin_n, t_sampled, clks, clkh)
%VALIDATE_INPUTS_V3_RESTRUCTURED 验证V3重构版本输入参数

    % 基本参数验证
    if length(Vin_p) ~= length(Vin_n) || length(Vin_p) ~= length(t_sampled) || ...
       length(clks) ~= length(t_sampled) || length(clkh) ~= length(t_sampled)
        error('输入信号和时间向量长度不一致');
    end

    % 确保列向量格式
    if isrow(Vin_p), Vin_p = Vin_p(:); end
    if isrow(Vin_n), Vin_n = Vin_n(:); end
    if isrow(t_sampled), t_sampled = t_sampled(:); end
    if isrow(clks), clks = clks(:); end
    if isrow(clkh), clkh = clkh(:); end
end

function [adc_output, binary_output, stage_history, performance_metrics] = ...
    finalize_pipeline_processing_v3_restructured(adc_output, binary_output, stage_history, params)
%FINALIZE_PIPELINE_PROCESSING_V3_RESTRUCTURED V3重构版本后处理

    % 计算性能指标
    num_samples = length(adc_output);
    valid_outputs = sum(abs(adc_output) > 0.001);
    valid_rate = valid_outputs / num_samples;

    performance_metrics = struct();
    performance_metrics.total_samples = num_samples;
    performance_metrics.valid_outputs = valid_outputs;
    performance_metrics.valid_rate = valid_rate;
    performance_metrics.signal_range = [min(adc_output), max(adc_output)];
    
    % 计算信号质量指标
    if valid_outputs > 0
        valid_indices = find(abs(adc_output) > 0.001);
        valid_signal = adc_output(valid_indices);
        performance_metrics.signal_power = mean(valid_signal.^2);
        performance_metrics.signal_std = std(valid_signal);
        performance_metrics.signal_peak = max(abs(valid_signal));
    else
        performance_metrics.signal_power = 0;
        performance_metrics.signal_std = 0;
        performance_metrics.signal_peak = 0;
    end

    % 标签统计
    if isfield(stage_history, 'sha_tags')
        unique_tags = unique(stage_history.sha_tags(stage_history.sha_tags > 0));
        performance_metrics.unique_tags = length(unique_tags);
        if ~isempty(unique_tags)
            performance_metrics.tag_range = [min(unique_tags), max(unique_tags)];
        else
            performance_metrics.tag_range = [0, 0];
        end
    else
        performance_metrics.unique_tags = 0;
        performance_metrics.tag_range = [0, 0];
    end

    performance_metrics.version = 'v3_restructured';

    fprintf('后处理完成: 有效输出%.1f%%, 峰值%.3fV, 标签%d\n', ...
            valid_rate*100, performance_metrics.signal_peak, performance_metrics.unique_tags);
end

 
function [adc_output, binary_output, stage_history, timing_info] = ...
    event_processing_core_v4(Vin_p, Vin_n, t_sampled, clks_edges, clkh_edges, ...
                            timing_control, rel_error_params, params)
%EVENT_PROCESSING_CORE_V4 事件驱动处理核心 (v4版本)
%   VERSION HISTORY:
%   v1: 原始版本 - 基础事件驱动处理功能
%   v2: 优化延迟缓冲机制和数字校正算法
%   v3: 简化时序控制逻辑，移除复杂延迟缓冲
%   v4: 严格时钟边沿触发逻辑 + 分离流水线架构 + 标签化对齐
%
%   v4版本主要特点:
%   1. 基于V2的正确事件处理功能重新设计
%   2. 严格的时钟边沿触发逻辑：基于边沿有效信号
%   3. 分离的流水线架构：每级独立处理，严格按时序传递
%   4. 真正的级间延迟控制：后级等待前级数据完成
%   5. 标签化数据对齐系统：SHA输出分配标签，贯穿流水线
%   6. 修复V2中的索引计算错误和时序同步问题
%
%   时钟边沿触发逻辑规范:
%   CLKS上升沿: 奇数级(1,3,5,7)+Flash接受输入, SHA+偶数级(2,4,6,8)开始输出
%   CLKS下降沿: 奇数级+Flash采样锁存, SHA+偶数级结束输出
%   CLKH上升沿: 奇数级+Flash开始输出, SHA+偶数级接受输入
%   CLKH下降沿: 奇数级+Flash结束输出, SHA+偶数级采样锁存
%
%   输入参数:
%       Vin_p, Vin_n - 差分输入信号
%       t_sampled - 时间向量
%       clks_edges, clkh_edges - 边沿有效信号结构体 (.rising, .falling)
%       timing_control - 时序控制参数
%       rel_error_params - 相对误差参数
%       params - ADC参数结构体
%
%   输出参数:
%       adc_output - 数字校正后的模拟输出
%       binary_output - 10bit数字输出
%       stage_history - 各级历史数据（带标签信息）
%       timing_info - V4版本处理统计信息

    fprintf('=== 事件驱动处理核心 v4.0 ===\n');
    fprintf('特性：严格边沿触发 + 分离流水线架构 + 标签化对齐\n');
    
    num_samples = length(t_sampled);
    num_stages = params.num_stages;
    
    % V4版本：初始化输出结构（带标签信息）
    adc_output = zeros(num_samples, 1);
    binary_output = zeros(num_samples, 10);
    stage_history = initialize_stage_history_unified(num_samples, num_stages);
    timing_info = struct('processed_events', 0, 'valid_outputs', 0, ...
                        'sha_updates', 0, 'pipeline_updates', 0, 'flash_updates', 0, ...
                        'clks_rising_events', 0, 'clks_falling_events', 0, ...
                        'clkh_rising_events', 0, 'clkh_falling_events', 0, ...
                        'tag_assignments', 0, 'data_propagations', 0, 'version', 'v4.0');
    
    % 计算差分输入
    Vin_diff = (Vin_p - Vin_n) / 2;
    
    % V4版本：初始化分离的流水线状态机
    pipeline_state = initialize_separated_pipeline_state_v4(num_stages, timing_control);
    
    % V4版本：标签化数据管理器
    tag_manager = initialize_tag_manager_v4();
    
    % V4版本：处理事件标记（用于后续数字校正）
    process_event_flags = false(num_samples, 1);
    
    fprintf('初始化v4分离流水线: %d个级别, %d个样本\n', num_stages, num_samples);
    
    %% V4版本：主要事件驱动循环 - 基于边沿有效信号
    fprintf('开始v4边沿触发事件处理...\n');
    
    for time_idx = 1:num_samples
        current_time = t_sampled(time_idx);
        current_input = Vin_diff(time_idx);
        
        % V4版本：SHA输入数据更新（在每个时间点都更新）
        pipeline_state.sha.input_data = current_input;
        pipeline_state.sha.valid_input = true;
        
        % 检测当前时间点的边沿事件
        clks_rising = clks_edges.rising(time_idx);
        clks_falling = clks_edges.falling(time_idx);
        clkh_rising = clkh_edges.rising(time_idx);
        clkh_falling = clkh_edges.falling(time_idx);
        
        %% V4版本：CLKS上升沿处理
        if clks_rising
            [pipeline_state, stage_history, timing_info, tag_manager] = ...
                process_clks_rising_v4(pipeline_state, stage_history, timing_info, ...
                                      tag_manager, time_idx, current_time, ...
                                      current_input, rel_error_params, params);
            timing_info.clks_rising_events = timing_info.clks_rising_events + 1;
        end
        
        %% V4版本：CLKS下降沿处理
        if clks_falling
            [pipeline_state, stage_history, timing_info] = ...
                process_clks_falling_v4(pipeline_state, stage_history, timing_info, ...
                                       time_idx, current_time, rel_error_params, params);
            timing_info.clks_falling_events = timing_info.clks_falling_events + 1;
        end
        
        %% V4版本：CLKH上升沿处理
        if clkh_rising
            [pipeline_state, stage_history, timing_info] = ...
                process_clkh_rising_v4(pipeline_state, stage_history, timing_info, ...
                                      time_idx, current_time, rel_error_params, params);
            timing_info.clkh_rising_events = timing_info.clkh_rising_events + 1;
        end
        
        %% V4版本：CLKH下降沿处理
        if clkh_falling
            [pipeline_state, stage_history, timing_info] = ...
                process_clkh_falling_v4(pipeline_state, stage_history, timing_info, ...
                                       time_idx, current_time, current_input, rel_error_params, params);
            timing_info.clkh_falling_events = timing_info.clkh_falling_events + 1;
        end
        
        %% V4版本：标记有事件处理的时间点
        if any([clks_rising, clks_falling, clkh_rising, clkh_falling])
            process_event_flags(time_idx) = true;
            timing_info.processed_events = timing_info.processed_events + 1;
        end
        
        % 定期显示进度（减少输出频率）
        if mod(time_idx, 10000) == 0
            fprintf('时间点 %d/%d: CLKS事件=%d, CLKH事件=%d, 标签分配=%d\n', ...
                    time_idx, num_samples, ...
                    timing_info.clks_rising_events + timing_info.clks_falling_events, ...
                    timing_info.clkh_rising_events + timing_info.clkh_falling_events, ...
                    timing_info.tag_assignments);
        end
    end
    
    %% V4版本：统一数字校正处理（调用digital_correction_v3模块）
    fprintf('v4后处理：调用标签化数字校正v3模块...\n');
    try
        % 创建简化的timing_control结构，避免struct转换错误
        simplified_timing_control = struct();
        simplified_timing_control.fs = params.fs;
        simplified_timing_control.version = 'v4_simplified';
        
        [corrected_output, corrected_binary, correction_metrics] = ...
            digital_correction_v3(stage_history, simplified_timing_control, params);
        
        % 将校正结果合并到输出
        valid_indices = find(abs(corrected_output) > 0.001);
        for i = 1:length(valid_indices)
            idx = valid_indices(i);
            if idx <= length(adc_output)
                adc_output(idx) = corrected_output(idx);
                binary_output(idx, :) = corrected_binary(idx, :);
            end
        end
        
        % 更新时序信息
        timing_info.correction_metrics = correction_metrics;
        fprintf('数字校正v3处理完成: %d个有效校正\n', correction_metrics.valid_corrections);
        
    catch ME
        warning('EVENT_PROCESSING_CORE_V4:CorrectionError', ...
                '标签化数字校正失败: %s', ME.message);
        timing_info.correction_metrics = struct('error', ME.message);
    end
    
    % 统计最终处理结果
    timing_info.valid_outputs = sum(abs(adc_output) > 0.001);
    timing_info.correction_rate = timing_info.valid_outputs / num_samples;
    
    fprintf('事件驱动核心v4处理完成:\n');
    fprintf('  边沿事件: CLKS=%d, CLKH=%d\n', ...
            timing_info.clks_rising_events + timing_info.clks_falling_events, ...
            timing_info.clkh_rising_events + timing_info.clkh_falling_events);
    fprintf('  标签分配: %d个, 数据传播: %d次\n', ...
            timing_info.tag_assignments, timing_info.data_propagations);
    fprintf('  有效输出: %d/%d (%.1f%%)\n', ...
            timing_info.valid_outputs, num_samples, timing_info.correction_rate*100);
end

function stage_history = initialize_stage_history_unified(num_samples, num_stages)
%INITIALIZE_STAGE_HISTORY_UNIFIED 统一的级历史记录初始化
%   整合v2, v3版本的功能，提供统一接口
%   
%   输入参数:
%       num_samples - 样本数量
%       num_stages - 流水线级数
%
%   输出参数:
%       stage_history - 历史记录结构体

    stage_history = struct();
    
    % SHA历史记录
    stage_history.sha_data = zeros(num_samples, 1);
    stage_history.sha_tags = zeros(num_samples, 1);
    stage_history.sha_timestamps = zeros(num_samples, 1);
    
    % 流水线级历史记录（每级包含模拟和数字输出）
    for stage = 1:num_stages
        analog_field = sprintf('stage_%d_analog', stage);
        digital_field = sprintf('stage_%d_digital', stage);
        tag_field = sprintf('stage_%d_tags', stage);
        timestamp_field = sprintf('stage_%d_timestamps', stage);
        
        stage_history.(analog_field) = zeros(num_samples, 1);
        stage_history.(digital_field) = zeros(num_samples, 2);  % 1.5bit输出
        stage_history.(tag_field) = zeros(num_samples, 1);
        stage_history.(timestamp_field) = zeros(num_samples, 1);
    end
    
    % Flash ADC历史记录
    stage_history.flash = zeros(num_samples, 2);  % 2bit输出
    stage_history.flash_tags = zeros(num_samples, 1);
    stage_history.flash_timestamps = zeros(num_samples, 1);
    
    % 统计和调试信息
    stage_history.stats = struct();
    stage_history.stats.total_samples = num_samples;
    stage_history.stats.num_stages = num_stages;
    stage_history.stats.creation_time = datetime('now');
end

% 保持向后兼容的函数别名
function stage_history = initialize_stage_history_v4(num_samples, num_stages)
    stage_history = initialize_stage_history_unified(num_samples, num_stages);
end

function pipeline_state = initialize_separated_pipeline_state_v4(num_stages, timing_control)
%INITIALIZE_SEPARATED_PIPELINE_STATE_V4 初始化分离的流水线状态机 - 修复时序死锁

    pipeline_state = struct();
    
    % V4版本：SHA状态（采样态CLKH，保持态CLKS） - 添加latched_ready状态支持
    pipeline_state.sha = struct(...
        'input_data', 0, ...
        'latched_data', 0, ...
        'output_data', 0, ...
        'state', 'idle', ...           % 'idle', 'latched_ready', 'holding'
        'valid_input', false, ...
        'valid_output', false, ...
        'clock_phase', 'none', ...     % 'clkh_to_clks_transition', 'clks_hold', 'none'
        'data_tag', 0, ...
        'last_update_time', 0);
    
    % V4版本：分离的流水线级状态 - 添加latched_ready状态支持
    for stage = 1:num_stages
        stage_field = sprintf('stage_%d', stage);
        
        % 确定时钟相位：奇数级(CLKS采样,CLKH保持)，偶数级(CLKH采样,CLKS保持)
        if mod(stage, 2) == 1  % 奇数级
            sample_phase = 'clks_sample';
            hold_phase = 'clkh_hold';
        else  % 偶数级
            sample_phase = 'clkh_sample';
            hold_phase = 'clks_hold';
        end
        
        pipeline_state.(stage_field) = struct(...
            'input_data', 0, ...
            'latched_data', 0, ...
            'analog_output', 0, ...
            'digital_output', [0, 0], ...
            'state', 'idle', ...           % 'idle', 'latched_ready', 'holding'
            'valid_input', false, ...
            'valid_analog_output', false, ...
            'valid_digital_output', false, ...
            'clock_phase', 'none', ...     % 'clks_to_clkh_transition', 'clkh_to_clks_transition', 'clks_hold', 'clkh_hold', 'none'
            'sample_phase', sample_phase, ...
            'hold_phase', hold_phase, ...
            'data_tag', 0, ...
            'last_update_time', 0, ...
            'propagation_delay', 0);       % V4版本：级间传播延迟
    end
    
    % V4版本：Flash ADC状态（采样态CLKS，保持态CLKH） - 添加latched_ready状态支持
    pipeline_state.flash = struct(...
        'input_data', 0, ...
        'latched_data', 0, ...
        'digital_output', [0, 0], ...
        'state', 'idle', ...           % 'idle', 'latched_ready', 'holding'
        'valid_input', false, ...
        'valid_output', false, ...
        'clock_phase', 'none', ...     % 'clks_to_clkh_transition', 'clkh_hold', 'none'
        'data_tag', 0, ...
        'delay_counter', 0, ...        % 4.5周期延迟计数器
        'last_update_time', 0);
    
    fprintf('分离流水线状态机v4初始化完成: %d级 (支持修复的时序状态)\n', num_stages);
    fprintf('状态转换序列: idle → latched_ready → holding → idle\n');
    fprintf('开关电容时序: 下降沿锁存 → 非交叠间隙(latched_ready) → 上升沿输出(holding)\n');
end

function tag_manager = initialize_tag_manager_v4()
%INITIALIZE_TAG_MANAGER_V4 初始化标签管理器

    tag_manager = struct();
    tag_manager.current_tag_counter = 1;  % 全局唯一标签计数器
    tag_manager.active_tags = [];         % 当前活跃标签列表
    tag_manager.tag_assignment_log = [];  % 标签分配历史（数值数组）
    tag_manager.tag_assignment_times = []; % 标签分配时间
    tag_manager.tag_assignment_indices = []; % 标签分配索引
    tag_manager.tag_completion_log = [];  % 标签完成历史
end

function [pipeline_state, stage_history, timing_info, tag_manager] = ...
    process_clks_rising_v4(pipeline_state, stage_history, timing_info, tag_manager, ...
                          time_idx, current_time, input_voltage, rel_error_params, params)
%PROCESS_CLKS_RISING_V4 处理CLKS上升沿事件 - 修复时序死锁

    num_stages = params.num_stages;
    
    %% 1. 奇数级流水线（STAGE1,3,5,7）和Flash ADC：接受前级输入信号并更新内部数据
    
    % STAGE1从SHA接受输入 - 修复：检查SHA的latched_ready状态用于数据传播
    if pipeline_state.sha.valid_input && strcmp(pipeline_state.sha.state, 'latched_ready')
        pipeline_state.stage_1.input_data = pipeline_state.sha.output_data;
        pipeline_state.stage_1.data_tag = pipeline_state.sha.data_tag;
        pipeline_state.stage_1.valid_input = true;
        pipeline_state.stage_1.last_update_time = current_time;
        timing_info.data_propagations = timing_info.data_propagations + 1;
    end
    
    % 其他奇数级（STAGE3,5,7）从前一级接受输入 - 修复：检查latched_ready状态
    for stage = 3:2:num_stages
        stage_field = sprintf('stage_%d', stage);
        prev_stage_field = sprintf('stage_%d', stage-1);
        
        if (pipeline_state.(prev_stage_field).valid_analog_output && ...
           strcmp(pipeline_state.(prev_stage_field).state, 'holding')) || ...
           (pipeline_state.(prev_stage_field).valid_input && ...
           strcmp(pipeline_state.(prev_stage_field).state, 'latched_ready'))
            
            % 根据前级状态获取数据
            if strcmp(pipeline_state.(prev_stage_field).state, 'holding')
                input_data = pipeline_state.(prev_stage_field).analog_output;
            else  % latched_ready状态，使用锁存数据执行快速处理
                [temp_analog, ~] = process_1p5bit_pipeline_stage_unified(...
                    pipeline_state.(prev_stage_field).latched_data, stage-1, rel_error_params, params);
                input_data = temp_analog;
            end
            
            pipeline_state.(stage_field).input_data = input_data;
            pipeline_state.(stage_field).data_tag = pipeline_state.(prev_stage_field).data_tag;
            pipeline_state.(stage_field).valid_input = true;
            pipeline_state.(stage_field).last_update_time = current_time;
            timing_info.data_propagations = timing_info.data_propagations + 1;
        end
    end
    
    % Flash ADC从最后一级接受输入 - 修复：检查latched_ready状态
    last_stage_field = sprintf('stage_%d', num_stages);
    if (pipeline_state.(last_stage_field).valid_analog_output && ...
       strcmp(pipeline_state.(last_stage_field).state, 'holding')) || ...
       (pipeline_state.(last_stage_field).valid_input && ...
       strcmp(pipeline_state.(last_stage_field).state, 'latched_ready'))
        
        % 根据最后一级状态获取数据
        if strcmp(pipeline_state.(last_stage_field).state, 'holding')
            input_data = pipeline_state.(last_stage_field).analog_output;
        else  % latched_ready状态，使用锁存数据执行快速处理
            [temp_analog, ~] = process_1p5bit_pipeline_stage_unified(...
                pipeline_state.(last_stage_field).latched_data, num_stages, rel_error_params, params);
            input_data = temp_analog;
        end
        
        pipeline_state.flash.input_data = input_data;
        pipeline_state.flash.data_tag = pipeline_state.(last_stage_field).data_tag;
        pipeline_state.flash.valid_input = true;
        pipeline_state.flash.last_update_time = current_time;
        timing_info.data_propagations = timing_info.data_propagations + 1;
    end
    
    %% 2. SHA和偶数级流水线（STAGE2,4,6,8）：开始输出已处理的数据并保持输出状态
    %% 修复：正确检查latched_ready状态而不是错误的sampling状态
    
    % SHA开始保持态输出（修复：检查latched_ready状态）
    if pipeline_state.sha.valid_input && strcmp(pipeline_state.sha.state, 'latched_ready')
        % SHA转换到holding状态并输出稳定的锁存值
        pipeline_state.sha.state = 'holding';
        pipeline_state.sha.valid_output = true;
        pipeline_state.sha.clock_phase = 'clks_hold';
        
        % 记录SHA输出到历史
        stage_history.sha_data(time_idx) = pipeline_state.sha.output_data;
        stage_history.sha_tags(time_idx) = pipeline_state.sha.data_tag;
        stage_history.sha_timestamps(time_idx) = current_time;
        timing_info.sha_updates = timing_info.sha_updates + 1;
    end
    
    % 偶数级（STAGE2,4,6,8）开始保持态输出（修复：检查latched_ready状态）
    for stage = 2:2:num_stages
        stage_field = sprintf('stage_%d', stage);
        
        if pipeline_state.(stage_field).valid_input && ...
           strcmp(pipeline_state.(stage_field).state, 'latched_ready')
            
            % 执行1.5bit ADC + MDAC处理
            [analog_out, digital_out] = process_1p5bit_pipeline_stage_unified(...
                pipeline_state.(stage_field).latched_data, stage, rel_error_params, params);
            
            % 更新输出状态
            pipeline_state.(stage_field).analog_output = analog_out;
            pipeline_state.(stage_field).digital_output = digital_out;
            pipeline_state.(stage_field).state = 'holding';
            pipeline_state.(stage_field).valid_analog_output = true;
            pipeline_state.(stage_field).valid_digital_output = true;
            pipeline_state.(stage_field).clock_phase = 'clks_hold';
            
            % 记录到历史（带标签）
            analog_field = sprintf('stage_%d_analog', stage);
            digital_field = sprintf('stage_%d_digital', stage);
            tag_field = sprintf('stage_%d_tags', stage);
            timestamp_field = sprintf('stage_%d_timestamps', stage);
            
            stage_history.(analog_field)(time_idx) = analog_out;
            stage_history.(digital_field)(time_idx, :) = digital_out;
            stage_history.(tag_field)(time_idx) = pipeline_state.(stage_field).data_tag;
            stage_history.(timestamp_field)(time_idx) = current_time;
            
            timing_info.pipeline_updates = timing_info.pipeline_updates + 1;
        end
    end
    
    %% 3. 数据传播：奇数级和Flash ADC接受前级输入信号并更新内部数据
    
    % SHA接受外部输入信号（从前级传播） - 修复：检查SHA的holding状态用于数据传播
    if pipeline_state.sha.valid_output && strcmp(pipeline_state.sha.state, 'holding')
        % SHA数据传播到STAGE1
        pipeline_state.stage_1.input_data = pipeline_state.sha.output_data;
        pipeline_state.stage_1.data_tag = pipeline_state.sha.data_tag;
        pipeline_state.stage_1.valid_input = true;
        pipeline_state.stage_1.last_update_time = current_time;
        timing_info.data_propagations = timing_info.data_propagations + 1;
    end
    
    % 偶数级（STAGE2,4,6,8）从前一级接受输入 - 修复：检查latched_ready状态
    for stage = 2:2:num_stages
        stage_field = sprintf('stage_%d', stage);
        prev_stage_field = sprintf('stage_%d', stage-1);
        
        if (pipeline_state.(prev_stage_field).valid_analog_output && ...
           strcmp(pipeline_state.(prev_stage_field).state, 'holding')) || ...
           (pipeline_state.(prev_stage_field).valid_input && ...
           strcmp(pipeline_state.(prev_stage_field).state, 'latched_ready'))
            
            % 根据前级状态获取数据
            if strcmp(pipeline_state.(prev_stage_field).state, 'holding')
                input_data = pipeline_state.(prev_stage_field).analog_output;
            else  % latched_ready状态，使用锁存数据执行快速处理
                [temp_analog, ~] = process_1p5bit_pipeline_stage_unified(...
                    pipeline_state.(prev_stage_field).latched_data, stage-1, rel_error_params, params);
                input_data = temp_analog;
            end
            
            pipeline_state.(stage_field).input_data = input_data;
            pipeline_state.(stage_field).data_tag = pipeline_state.(prev_stage_field).data_tag;
            pipeline_state.(stage_field).valid_input = true;
            pipeline_state.(stage_field).last_update_time = current_time;
            timing_info.data_propagations = timing_info.data_propagations + 1;
        end
    end
    
    % Flash ADC从最后一级接受输入 - 修复：检查latched_ready状态
    if num_stages >= 1
        last_stage_field = sprintf('stage_%d', num_stages);
        if (pipeline_state.(last_stage_field).valid_analog_output && ...
           strcmp(pipeline_state.(last_stage_field).state, 'holding')) || ...
           (pipeline_state.(last_stage_field).valid_input && ...
           strcmp(pipeline_state.(last_stage_field).state, 'latched_ready'))
            
            % 根据最后一级状态获取数据
            if strcmp(pipeline_state.(last_stage_field).state, 'holding')
                input_data = pipeline_state.(last_stage_field).analog_output;
            else  % latched_ready状态，使用锁存数据执行快速处理
                [temp_analog, ~] = process_1p5bit_pipeline_stage_unified(...
                    pipeline_state.(last_stage_field).latched_data, num_stages, rel_error_params, params);
                input_data = temp_analog;
            end
            
            pipeline_state.flash.input_data = input_data;
            pipeline_state.flash.data_tag = pipeline_state.(last_stage_field).data_tag;
            pipeline_state.flash.valid_input = true;
            pipeline_state.flash.last_update_time = current_time;
            timing_info.data_propagations = timing_info.data_propagations + 1;
        end
    end
    
    %% 4. SHA标签分配（CLKS上升沿时分配新标签）
    if abs(input_voltage) > 0.001  % 有有效输入信号
        % 为SHA输出分配新的全局唯一标签
        new_tag = tag_manager.current_tag_counter;
        pipeline_state.sha.data_tag = new_tag;
        
        % 更新标签管理器
        tag_manager.current_tag_counter = tag_manager.current_tag_counter + 1;
        tag_manager.active_tags(end+1) = new_tag;
        tag_manager.tag_assignment_log(end+1) = new_tag;
        tag_manager.tag_assignment_times(end+1) = current_time;
        tag_manager.tag_assignment_indices(end+1) = time_idx;
        
        timing_info.tag_assignments = timing_info.tag_assignments + 1;
    end
end

function [pipeline_state, stage_history, timing_info] = ...
    process_clks_falling_v4(pipeline_state, stage_history, timing_info, ...
                           time_idx, current_time, rel_error_params, params)
%PROCESS_CLKS_FALLING_V4 处理CLKS下降沿事件 - 修复时序一致性

    num_stages = params.num_stages;
    
    %% 1. 奇数级流水线和Flash ADC：采样并锁存当前输入数据
    %% 修复：正确处理开关电容时序 - 立即转换到latched_ready状态
    
    % STAGE1采样并锁存来自SHA的数据
    if pipeline_state.stage_1.valid_input
        pipeline_state.stage_1.latched_data = pipeline_state.stage_1.input_data;
        % 修复：CLKS下降沿立即准备好输出
        pipeline_state.stage_1.state = 'latched_ready';
        pipeline_state.stage_1.clock_phase = 'clks_to_clkh_transition';
    end
    
    % 其他奇数级（STAGE3,5,7）采样并锁存
    for stage = 3:2:num_stages
        stage_field = sprintf('stage_%d', stage);
        
        if pipeline_state.(stage_field).valid_input
            pipeline_state.(stage_field).latched_data = pipeline_state.(stage_field).input_data;
            % 修复：CLKS下降沿立即准备好输出
            pipeline_state.(stage_field).state = 'latched_ready';
            pipeline_state.(stage_field).clock_phase = 'clks_to_clkh_transition';
        end
    end
    
    % Flash ADC采样并锁存
    if pipeline_state.flash.valid_input
        pipeline_state.flash.latched_data = pipeline_state.flash.input_data;
        % 修复：Flash ADC在CLKS下降沿立即准备好输出
        pipeline_state.flash.state = 'latched_ready';
        pipeline_state.flash.clock_phase = 'clks_to_clkh_transition';
    end
    
    %% 2. SHA和偶数级流水线：结束数据输出状态
    
    % SHA结束保持态
    if strcmp(pipeline_state.sha.state, 'holding')
        pipeline_state.sha.state = 'idle';
        pipeline_state.sha.clock_phase = 'none';
        pipeline_state.sha.valid_output = false;  % 清除输出有效标志
    end
    
    % 偶数级（STAGE2,4,6,8）结束保持态
    for stage = 2:2:num_stages
        stage_field = sprintf('stage_%d', stage);
        
        if strcmp(pipeline_state.(stage_field).state, 'holding')
            pipeline_state.(stage_field).state = 'idle';
            pipeline_state.(stage_field).clock_phase = 'none';
            pipeline_state.(stage_field).valid_analog_output = false;  % 清除输出有效标志
            pipeline_state.(stage_field).valid_digital_output = false;
        end
    end
end

function [pipeline_state, stage_history, timing_info] = ...
    process_clkh_rising_v4(pipeline_state, stage_history, timing_info, ...
                          time_idx, current_time, rel_error_params, params)
%PROCESS_CLKH_RISING_V4 处理CLKH上升沿事件 - 修复时序一致性

    num_stages = params.num_stages;
    
    %% 1. 奇数级流水线和Flash ADC：开始输出已处理的数据并保持输出状态
    
    % STAGE1开始保持态输出（修复：检查latched_ready状态）
    if pipeline_state.stage_1.valid_input && strcmp(pipeline_state.stage_1.state, 'latched_ready')
        % 执行1.5bit ADC + MDAC处理
        [analog_out, digital_out] = process_1p5bit_pipeline_stage_unified(...
            pipeline_state.stage_1.latched_data, 1, rel_error_params, params);
        
        % 更新输出状态
        pipeline_state.stage_1.analog_output = analog_out;
        pipeline_state.stage_1.digital_output = digital_out;
        pipeline_state.stage_1.state = 'holding';
        pipeline_state.stage_1.valid_analog_output = true;
        pipeline_state.stage_1.valid_digital_output = true;
        pipeline_state.stage_1.clock_phase = 'clkh_hold';
        
        % 记录到历史（带标签）
        stage_history.stage_1_analog(time_idx) = analog_out;
        stage_history.stage_1_digital(time_idx, :) = digital_out;
        stage_history.stage_1_tags(time_idx) = pipeline_state.stage_1.data_tag;
        stage_history.stage_1_timestamps(time_idx) = current_time;
        
        timing_info.pipeline_updates = timing_info.pipeline_updates + 1;
    end
    
    % 其他奇数级（STAGE3,5,7）开始保持态输出（修复：检查latched_ready状态）
    for stage = 3:2:num_stages
        stage_field = sprintf('stage_%d', stage);
        
        if pipeline_state.(stage_field).valid_input && ...
           strcmp(pipeline_state.(stage_field).state, 'latched_ready')
            
            % 执行1.5bit ADC + MDAC处理
            [analog_out, digital_out] = process_1p5bit_pipeline_stage_unified(...
                pipeline_state.(stage_field).latched_data, stage, rel_error_params, params);
            
            % 更新输出状态
            pipeline_state.(stage_field).analog_output = analog_out;
            pipeline_state.(stage_field).digital_output = digital_out;
            pipeline_state.(stage_field).state = 'holding';
            pipeline_state.(stage_field).valid_analog_output = true;
            pipeline_state.(stage_field).valid_digital_output = true;
            pipeline_state.(stage_field).clock_phase = 'clkh_hold';
            
            % 记录到历史（带标签）
            analog_field = sprintf('stage_%d_analog', stage);
            digital_field = sprintf('stage_%d_digital', stage);
            tag_field = sprintf('stage_%d_tags', stage);
            timestamp_field = sprintf('stage_%d_timestamps', stage);
            
            stage_history.(analog_field)(time_idx) = analog_out;
            stage_history.(digital_field)(time_idx, :) = digital_out;
            stage_history.(tag_field)(time_idx) = pipeline_state.(stage_field).data_tag;
            stage_history.(timestamp_field)(time_idx) = current_time;
            
            timing_info.pipeline_updates = timing_info.pipeline_updates + 1;
        end
    end
    
    % Flash ADC开始保持态输出（考虑4.5周期延迟）- 修复：检查latched_ready状态
    if pipeline_state.flash.valid_input && strcmp(pipeline_state.flash.state, 'latched_ready')
        pipeline_state.flash.delay_counter = pipeline_state.flash.delay_counter + 1;
        
        % 检查是否达到4.5周期延迟
        if pipeline_state.flash.delay_counter >= 9  % 4.5周期 = 9个半周期
            % 执行2bit Flash ADC量化
            flash_digital = process_2bit_flash_adc_v4(pipeline_state.flash.latched_data, params);
            
            pipeline_state.flash.digital_output = flash_digital;
            pipeline_state.flash.state = 'holding';
            pipeline_state.flash.valid_output = true;
            pipeline_state.flash.clock_phase = 'clkh_hold';
            
            % 记录到历史（带标签）
            stage_history.flash(time_idx, :) = flash_digital;
            stage_history.flash_tags(time_idx) = pipeline_state.flash.data_tag;
            stage_history.flash_timestamps(time_idx) = current_time;
            
            timing_info.flash_updates = timing_info.flash_updates + 1;
        end
    end
    
    %% 2. SHA和偶数级流水线：接受前级输入信号并更新内部数据
    
    % SHA接受外部输入信号（从前级传播） - 修复：检查SHA的holding状态用于数据传播
    if pipeline_state.sha.valid_output && strcmp(pipeline_state.sha.state, 'holding')
        % SHA数据传播到STAGE1
        pipeline_state.stage_1.input_data = pipeline_state.sha.output_data;
        pipeline_state.stage_1.data_tag = pipeline_state.sha.data_tag;
        pipeline_state.stage_1.valid_input = true;
        pipeline_state.stage_1.last_update_time = current_time;
        timing_info.data_propagations = timing_info.data_propagations + 1;
    end
    
    % 偶数级（STAGE2,4,6,8）从前一级接受输入 - 修复：检查latched_ready状态
    for stage = 2:2:num_stages
        stage_field = sprintf('stage_%d', stage);
        prev_stage_field = sprintf('stage_%d', stage-1);
        
        if (pipeline_state.(prev_stage_field).valid_analog_output && ...
           strcmp(pipeline_state.(prev_stage_field).state, 'holding')) || ...
           (pipeline_state.(prev_stage_field).valid_input && ...
           strcmp(pipeline_state.(prev_stage_field).state, 'latched_ready'))
            
            % 根据前级状态获取数据
            if strcmp(pipeline_state.(prev_stage_field).state, 'holding')
                input_data = pipeline_state.(prev_stage_field).analog_output;
            else  % latched_ready状态，使用锁存数据执行快速处理
                [temp_analog, ~] = process_1p5bit_pipeline_stage_unified(...
                    pipeline_state.(prev_stage_field).latched_data, stage-1, rel_error_params, params);
                input_data = temp_analog;
            end
            
            pipeline_state.(stage_field).input_data = input_data;
            pipeline_state.(stage_field).data_tag = pipeline_state.(prev_stage_field).data_tag;
            pipeline_state.(stage_field).valid_input = true;
            pipeline_state.(stage_field).last_update_time = current_time;
            timing_info.data_propagations = timing_info.data_propagations + 1;
        end
    end
    
    % Flash ADC从最后一级接受输入 - 修复：检查latched_ready状态
    if num_stages >= 1
        last_stage_field = sprintf('stage_%d', num_stages);
        if (pipeline_state.(last_stage_field).valid_analog_output && ...
           strcmp(pipeline_state.(last_stage_field).state, 'holding')) || ...
           (pipeline_state.(last_stage_field).valid_input && ...
           strcmp(pipeline_state.(last_stage_field).state, 'latched_ready'))
            
            % 根据最后一级状态获取数据
            if strcmp(pipeline_state.(last_stage_field).state, 'holding')
                input_data = pipeline_state.(last_stage_field).analog_output;
            else  % latched_ready状态，使用锁存数据执行快速处理
                [temp_analog, ~] = process_1p5bit_pipeline_stage_unified(...
                    pipeline_state.(last_stage_field).latched_data, num_stages, rel_error_params, params);
                input_data = temp_analog;
            end
            
            pipeline_state.flash.input_data = input_data;
            pipeline_state.flash.data_tag = pipeline_state.(last_stage_field).data_tag;
            pipeline_state.flash.valid_input = true;
            pipeline_state.flash.last_update_time = current_time;
            timing_info.data_propagations = timing_info.data_propagations + 1;
        end
    end
end

function [pipeline_state, stage_history, timing_info] = ...
    process_clkh_falling_v4(pipeline_state, stage_history, timing_info, ...
                           time_idx, current_time, input_voltage, rel_error_params, params)
%PROCESS_CLKH_FALLING_V4 处理CLKH下降沿事件 - 修复时序死锁

    num_stages = params.num_stages;
    
    %% 1. 奇数级流水线和Flash ADC：结束数据输出状态
    
    % STAGE1结束保持态
    if strcmp(pipeline_state.stage_1.state, 'holding')
        pipeline_state.stage_1.state = 'idle';
        pipeline_state.stage_1.clock_phase = 'none';
    end
    
    % 其他奇数级（STAGE3,5,7）结束保持态
    for stage = 3:2:num_stages
        stage_field = sprintf('stage_%d', stage);
        
        if strcmp(pipeline_state.(stage_field).state, 'holding')
            pipeline_state.(stage_field).state = 'idle';
            pipeline_state.(stage_field).clock_phase = 'none';
        end
    end
    
    % Flash ADC结束保持态
    if strcmp(pipeline_state.flash.state, 'holding')
        pipeline_state.flash.state = 'idle';
        pipeline_state.flash.clock_phase = 'none';
    end
    
    %% 2. SHA和偶数级流水线：采样并锁存当前输入数据
    %% 修复：SHA时序逻辑 - 开关电容正确行为
    
    % SHA采样外部输入信号并立即转换到holding状态
    if abs(input_voltage) > 0.001  % 有有效输入信号
        pipeline_state.sha.input_data = input_voltage;
        pipeline_state.sha.valid_input = true;
        pipeline_state.sha.latched_data = input_voltage;
        
        % 修复：SHA在CLKH下降沿立即锁存并准备在CLKS上升沿输出
        % 开关电容SHA的正确行为：CLKH下降→锁存→准备输出
        pipeline_state.sha.state = 'latched_ready';  % 新状态：已锁存，准备输出
        pipeline_state.sha.output_data = input_voltage;  % 预设输出数据
        pipeline_state.sha.clock_phase = 'clkh_to_clks_transition';
        
        % 记录SHA锁存时刻
        stage_history.sha_timestamps(time_idx) = current_time;
    end
    
    % 偶数级（STAGE2,4,6,8）采样并锁存，同样修复时序问题
    for stage = 2:2:num_stages
        stage_field = sprintf('stage_%d', stage);
        
        if pipeline_state.(stage_field).valid_input
            pipeline_state.(stage_field).latched_data = pipeline_state.(stage_field).input_data;
            % 修复：偶数级在CLKH下降沿也立即准备好输出
            pipeline_state.(stage_field).state = 'latched_ready';
            pipeline_state.(stage_field).clock_phase = 'clkh_to_clks_transition';
        end
    end
end

function [analog_out, digital_out] = process_1p5bit_pipeline_stage_unified(...
    input_data, stage_num, rel_error_params, params)
%PROCESS_1P5BIT_PIPELINE_STAGE_UNIFIED 统一的1.5bit流水线级处理
%   整合v1, v2, v4版本的功能，提供统一接口
%   
%   输入参数:
%       input_data - 输入电压值
%       stage_num - 流水线级数
%       rel_error_params - 相对误差参数 (可选)
%       params - ADC参数结构体
%
%   输出参数:
%       analog_out - 模拟输出(MDAC残余)
%       digital_out - 数字输出码[MSB, LSB]

    % 参数验证
    if nargin < 4
        error('PIPELINE_STAGE:InsufficientArgs', '至少需要4个输入参数');
    end
    
    % 1.5bit量化逻辑
    threshold = 0.25 * params.Vref;
    
    if input_data > threshold
        digital_code = [1, 0];  % +1 码
        residue = 2 * input_data - params.Vref;
    elseif input_data < -threshold
        digital_code = [0, 1];  % -1 码  
        residue = 2 * input_data + params.Vref;
    else
        digital_code = [0, 0];  % 0 码
        residue = 2 * input_data;
    end
    
    % 应用相对误差（如果提供）
    if nargin >= 3 && ~isempty(rel_error_params) && size(rel_error_params, 1) >= stage_num
        gain_error = rel_error_params(stage_num, 1);
        offset_error = 0;
        if size(rel_error_params, 2) >= 2
            offset_error = rel_error_params(stage_num, 2);
        end
        
        % 应用增益和偏移误差
        residue = residue * (1 + gain_error) + offset_error;
    end
    
    % 输出限幅
    max_output = params.Vref;
    residue = max(-max_output, min(max_output, residue));
    
    analog_out = residue;
    digital_out = digital_code;
end

function flash_digital = process_2bit_flash_adc_v4(input_value, params)
%PROCESS_2BIT_FLASH_ADC_V4 处理2bit Flash ADC (v4版本)

    Vref = params.Vref;
    
    % 2bit Flash ADC的量化逻辑
    if input_value < -Vref/2
        flash_digital = [0, 0];      % 00
    elseif input_value < 0
        flash_digital = [0, 1];      % 01
    elseif input_value < Vref/2
        flash_digital = [1, 0];      % 10
    else
        flash_digital = [1, 1];      % 11
    end
end

% 保持向后兼容的函数别名
function [analog_out, digital_out] = process_1p5bit_pipeline_stage_v4(...
    input_data, stage_num, rel_error_params, params)
    [analog_out, digital_out] = process_1p5bit_pipeline_stage_unified(...
        input_data, stage_num, rel_error_params, params);
end 
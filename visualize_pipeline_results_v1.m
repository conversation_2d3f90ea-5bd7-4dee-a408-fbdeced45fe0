function visualize_enhanced_pipeline_results(t_sampled, sine_input, analog_outputs, ...
    final_dac_output, ideal_dac_output, performance_metrics, params)
%VISUALIZE_ENHANCED_PIPELINE_RESULTS 可视化增强流水线ADC结果
%   专门用于显示理想状态流水线ADC的完整分析结果

    fprintf('生成增强流水线ADC可视化结果...\n');
    
    % 创建主图窗
    figure('Name', '增强理想状态流水线ADC分析结果', 'Position', [100, 100, 1400, 900]);
    
    %% 子图1: 输入信号和时钟
    subplot(3, 3, 1);
    plot(t_sampled*1e6, sine_input, 'b-', 'LineWidth', 1.5);
    title('输入正弦波信号');
    xlabel('时间 (μs)');
    ylabel('幅度 (V)');
    grid on;
    axis tight;
    
    %% 子图2: SHA级模拟输出
    subplot(3, 3, 2);
    valid_sha = abs(analog_outputs.sha) > 0.001;
    if any(valid_sha)
        plot(t_sampled(valid_sha)*1e6, analog_outputs.sha(valid_sha), 'r.-', 'MarkerSize', 4);
        title('SHA级模拟输出');
        xlabel('时间 (μs)');
        ylabel('幅度 (V)');
        grid on;
    else
        text(0.5, 0.5, '无有效SHA输出', 'HorizontalAlignment', 'center');
        title('SHA级模拟输出');
    end
    
    %% 子图3: 各级流水线模拟输出（选择性显示）
    subplot(3, 3, 3);
    colors = lines(params.num_stages);
    hold on;
    legend_entries = {};
    
    for stage = [1, 4, 8]  % 显示第1、4、8级作为代表
        if stage <= params.num_stages
            stage_output = analog_outputs.(['stage_', num2str(stage)]);
            valid_stage = abs(stage_output) > 0.001;
            if any(valid_stage)
                plot(t_sampled(valid_stage)*1e6, stage_output(valid_stage), ...
                     'Color', colors(stage, :), 'LineWidth', 1, 'Marker', '.', 'MarkerSize', 3);
                legend_entries{end+1} = sprintf('STAGE%d', stage);
            end
        end
    end
    
    if ~isempty(legend_entries)
        legend(legend_entries, 'Location', 'best');
        title('代表性流水线级模拟输出');
        xlabel('时间 (μs)');
        ylabel('幅度 (V)');
        grid on;
    else
        text(0.5, 0.5, '无有效流水线输出', 'HorizontalAlignment', 'center');
        title('流水线级模拟输出');
    end
    hold off;
    
    %% 子图4: 最终DAC输出 vs 理想ADC输出
    subplot(3, 3, 4);
    valid_final = abs(final_dac_output) > 0.001;
    valid_ideal = abs(ideal_dac_output) > 0.001;
    
    hold on;
    if any(valid_final)
        plot(t_sampled(valid_final)*1e6, final_dac_output(valid_final), 'r-', ...
             'LineWidth', 2, 'DisplayName', '流水线ADC输出');
    end
    if any(valid_ideal)
        plot(t_sampled(valid_ideal)*1e6, ideal_dac_output(valid_ideal), 'b--', ...
             'LineWidth', 1.5, 'DisplayName', '理想ADC输出');
    end
    
    title('最终输出对比');
    xlabel('时间 (μs)');
    ylabel('幅度 (V)');
    legend('show');
    grid on;
    hold off;
    
    %% 子图5: 输出信号FFT频谱分析
    subplot(3, 3, 5);
    if any(valid_final) && length(find(valid_final)) >= 512
        final_data = final_dac_output(valid_final);
        if length(final_data) > 1024
            final_data = final_data(1:1024);
        end
        
        N = length(final_data);
        f_axis = (0:N/2-1) * params.fs / N / 1e6;  % MHz
        fft_data = abs(fft(final_data));
        fft_db = 20*log10(fft_data(1:N/2) / max(fft_data) + eps);
        
        plot(f_axis, fft_db, 'b-', 'LineWidth', 1);
        title('流水线ADC输出频谱');
        xlabel('频率 (MHz)');
        ylabel('幅度 (dB)');
        grid on;
        ylim([-120, 10]);
        
        % 标注基频
        [~, fundamental_idx] = max(fft_db);
        fundamental_freq = f_axis(fundamental_idx);
        hold on;
        plot(fundamental_freq, fft_db(fundamental_idx), 'ro', 'MarkerSize', 8);
        text(fundamental_freq, fft_db(fundamental_idx)+5, ...
             sprintf('f_0=%.2fMHz', fundamental_freq), 'FontSize', 8);
        hold off;
    else
        text(0.5, 0.5, '数据不足以进行FFT分析', 'HorizontalAlignment', 'center');
        title('频谱分析');
    end
    
    %% 子图6: 性能指标总结
    subplot(3, 3, 6);
    axis off;
    
    % 性能指标文本
    metrics_text = {
        '性能指标总结',
        '==================',
        sprintf('流水线ADC SNDR: %.2f dB', performance_metrics.pipeline_sndr),
        sprintf('理想ADC SNDR: %.2f dB', performance_metrics.ideal_sndr),
        sprintf('输出相关系数: %.4f', performance_metrics.correlation),
        sprintf('有效样本数: %d/%d', performance_metrics.valid_samples, performance_metrics.total_samples),
        sprintf('有效率: %.1f%%', performance_metrics.effectiveness),
        '',
        '系统参数',
        '==================',
        sprintf('采样频率: %.1f MHz', params.fs/1e6),
        sprintf('输入频率: %.2f MHz', params.f_in/1e6),
        sprintf('流水线级数: %d', params.num_stages),
        sprintf('参考电压: %.1f V', params.Vref)
    };
    
    text(0.05, 0.95, metrics_text, 'VerticalAlignment', 'top', ...
         'FontSize', 9, 'FontName', 'FixedWidth');
    
    %% 子图7: 各级数字输出热力图（抽样显示）
    subplot(3, 3, 7);
    try
        % 创建数字输出的可视化矩阵
        digital_matrix = create_digital_output_matrix(analog_outputs, params);
        
        if ~isempty(digital_matrix)
            imagesc(digital_matrix);
            colormap(gray);
            colorbar;
            title('各级输出活动热力图');
            xlabel('时间索引（抽样）');
            ylabel('级编号');
            
            % 设置Y轴标签
            stage_labels = {'SHA', 'ST1', 'ST2', 'ST3', 'ST4', 'ST5', 'ST6', 'ST7', 'ST8'};
            set(gca, 'YTick', 1:min(9, size(digital_matrix, 1)), ...
                     'YTickLabel', stage_labels(1:min(9, size(digital_matrix, 1))));
        else
            text(0.5, 0.5, '数字输出数据不可用', 'HorizontalAlignment', 'center');
            title('各级输出活动热力图');
        end
    catch
        text(0.5, 0.5, '热力图生成失败', 'HorizontalAlignment', 'center');
        title('各级输出活动热力图');
    end
    
    %% 子图8: 相位差分析
    subplot(3, 3, 8);
    if any(valid_final) && any(valid_ideal)
        % 找到共同的有效时间点
        common_valid = valid_final & valid_ideal;
        if sum(common_valid) > 100
            time_common = t_sampled(common_valid);
            final_common = final_dac_output(common_valid);
            ideal_common = ideal_dac_output(common_valid);
            
            % 选择前200个点进行相位分析
            if length(time_common) > 200
                indices = 1:200;
                time_plot = time_common(indices) * 1e6;
                final_plot = final_common(indices);
                ideal_plot = ideal_common(indices);
            else
                time_plot = time_common * 1e6;
                final_plot = final_common;
                ideal_plot = ideal_common;
            end
            
            hold on;
            plot(time_plot, final_plot, 'r-', 'LineWidth', 1.5, 'DisplayName', '流水线ADC');
            plot(time_plot, ideal_plot, 'b--', 'LineWidth', 1, 'DisplayName', '理想ADC');
            
            title('输出波形相位对比（局部）');
            xlabel('时间 (μs)');
            ylabel('幅度 (V)');
            legend('show');
            grid on;
            hold off;
        else
            text(0.5, 0.5, '有效数据不足', 'HorizontalAlignment', 'center');
            title('相位对比分析');
        end
    else
        text(0.5, 0.5, '输出数据不可用', 'HorizontalAlignment', 'center');
        title('相位对比分析');
    end
    
    %% 子图9: 误差分析
    subplot(3, 3, 9);
    if any(valid_final) && any(valid_ideal)
        common_valid = valid_final & valid_ideal;
        if sum(common_valid) > 10
            final_common = final_dac_output(common_valid);
            ideal_common = ideal_dac_output(common_valid);
            
            % 计算误差
            error_signal = final_common - ideal_common;
            
            if length(error_signal) > 200
                error_plot = error_signal(1:200);
                time_plot = (1:200);
            else
                error_plot = error_signal;
                time_plot = 1:length(error_signal);
            end
            
            plot(time_plot, error_signal*1000, 'g-', 'LineWidth', 1);
            title('输出误差信号');
            xlabel('样本索引');
            ylabel('误差 (mV)');
            grid on;
            
            % 显示RMS误差
            rms_error = sqrt(mean(error_signal.^2)) * 1000;
            text(0.7, 0.9, sprintf('RMS误差: %.2f mV', rms_error), ...
                 'Units', 'normalized', 'FontSize', 8, ...
                 'BackgroundColor', 'white', 'EdgeColor', 'black');
        else
            text(0.5, 0.5, '误差分析数据不足', 'HorizontalAlignment', 'center');
            title('误差分析');
        end
    else
        text(0.5, 0.5, '误差分析不可用', 'HorizontalAlignment', 'center');
        title('误差分析');
    end
    
    % 调整子图间距
    sgtitle('增强理想状态流水线ADC完整分析结果', 'FontSize', 14, 'FontWeight', 'bold');
    
    % 保存图像
    save_enhanced_results_figure(params);
    
    fprintf('可视化结果生成完成\n');
end

function digital_matrix = create_digital_output_matrix(analog_outputs, params)
%CREATE_DIGITAL_OUTPUT_MATRIX 创建数字输出可视化矩阵

    try
        num_stages = params.num_stages + 1;  % 包括SHA
        
        % 获取所有模拟输出的长度
        sha_length = length(analog_outputs.sha);
        sample_step = max(1, floor(sha_length / 100));  % 抽样显示
        sample_indices = 1:sample_step:sha_length;
        
        digital_matrix = zeros(num_stages, length(sample_indices));
        
        % SHA级
        sha_valid = abs(analog_outputs.sha(sample_indices)) > 0.001;
        digital_matrix(1, :) = double(sha_valid);
        
        % 流水线级
        for stage = 1:params.num_stages
            if stage <= 8  % 确保不超过8级
                stage_field = ['stage_', num2str(stage)];
                if isfield(analog_outputs, stage_field)
                    stage_output = analog_outputs.(stage_field);
                    stage_valid = abs(stage_output(sample_indices)) > 0.001;
                    digital_matrix(stage + 1, :) = double(stage_valid);
                end
            end
        end
        
    catch ME
        fprintf('创建数字输出矩阵失败: %s\n', ME.message);
        digital_matrix = [];
    end
end

function save_enhanced_results_figure(params)
%SAVE_ENHANCED_RESULTS_FIGURE 保存增强结果图像

    try
        % 创建保存目录
        save_dir = 'improved_pipeline_adc/test_data';
        if ~exist(save_dir, 'dir')
            mkdir(save_dir);
        end
        
        % 生成文件名
        timestamp = datestr(now, 'yyyymmdd_HHMMSS');
        filename = sprintf('%s/enhanced_pipeline_adc_results_%s', save_dir, timestamp);
        
        % 保存为PNG和FIG格式
        saveas(gcf, [filename, '.png']);
        saveas(gcf, [filename, '.fig']);
        
        fprintf('结果图像已保存: %s.png/.fig\n', filename);
        
    catch ME
        fprintf('保存图像失败: %s\n', ME.message);
    end
end 
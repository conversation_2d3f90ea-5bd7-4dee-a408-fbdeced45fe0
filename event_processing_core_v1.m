function [adc_output, binary_output, stage_history, timing_info] = ...
    enhanced_event_processing_core(Vin_p, Vin_n, t_sampled, event_system, ...
                                 timing_control, rel_error_params, params)
%ENHANCED_EVENT_PROCESSING_CORE 增强的事件处理核心
%   根据详细技术规范实现完整的时序控制和数据处理
%   
%   技术规范实现:
%   1. SHA电路: 采样态时钟下降沿前更新并锁存，保持态时钟上升沿后输出
%   2. 1.5bit流水线级: 时钟下降沿锁存，上升沿保持输出，阈值[-1/4Vref, 1/4Vref]
%   3. Flash ADC: 数据延迟4.5个周期后进入，2bit完整量化
%   4. 数字校准: 延迟同步 + 18bit→10bit全加器累加 + 理想DAC转换

    num_samples = length(t_sampled);
    num_stages = params.num_stages;
    
    % 初始化输出
    adc_output = zeros(num_samples, 1);
    binary_output = zeros(num_samples, 10);
    stage_history = initialize_enhanced_stage_history(num_samples, num_stages);
    timing_info = struct('processed_events', 0, 'valid_outputs', 0, ...
                        'sha_updates', 0, 'pipeline_updates', 0, 'flash_updates', 0, ...
                        'clkh_events', 0, 'clks_events', 0, 'digital_corrections', 0);
    
    % 计算差分输入
    Vin_diff = (Vin_p - Vin_n) / 2;
    
    % 初始化流水线状态机
    pipeline_state = initialize_enhanced_pipeline_state(num_stages, timing_control);
    
    % 初始化数字校准延迟缓冲
    digital_correction_buffers = initialize_digital_correction_buffers(num_stages, timing_control);
    
    fprintf('开始增强事件处理: %d个事件\n', event_system.num_events);
    
    % 主事件驱动循环
    for event_idx = 1:event_system.num_events
        event = event_system.events(event_idx);
        sample_idx = event.index;
        
        % 确保索引在有效范围内
        if sample_idx <= 0 || sample_idx > num_samples
            continue;
        end
        
        % 根据技术规范处理不同类型的时钟事件
        switch event.type
            case 'CLKH_RISING'
                % CLKH上升沿: SHA保持态, 偶数级保持态
                [pipeline_state, stage_history, timing_info] = process_clkh_rising_enhanced(...
                    pipeline_state, stage_history, timing_info, ...
                    sample_idx, event.time, timing_control, params);
                timing_info.clkh_events = timing_info.clkh_events + 1;
                
            case 'CLKH_FALLING'
                % CLKH下降沿: SHA采样并锁存, 偶数级采样并锁存
                [pipeline_state, stage_history, timing_info] = process_clkh_falling_enhanced(...
                    pipeline_state, stage_history, timing_info, ...
                    Vin_diff(sample_idx), sample_idx, event.time, rel_error_params, timing_control, params);
                
            case 'CLKS_RISING'
                % CLKS上升沿: 奇数级保持态, Flash ADC保持态
                [pipeline_state, stage_history, timing_info] = process_clks_rising_enhanced(...
                    pipeline_state, stage_history, timing_info, ...
                    sample_idx, event.time, timing_control, params);
                timing_info.clks_events = timing_info.clks_events + 1;
                
            case 'CLKS_FALLING'
                % CLKS下降沿: 奇数级采样并锁存, Flash ADC采样并锁存, 执行数字校正
                [pipeline_state, stage_history, adc_output, binary_output, timing_info, digital_correction_buffers] = ...
                    process_clks_falling_enhanced(...
                    pipeline_state, stage_history, adc_output, binary_output, timing_info, ...
                    digital_correction_buffers, sample_idx, event.time, rel_error_params, timing_control, params);
        end
        
        timing_info.processed_events = timing_info.processed_events + 1;
        
        % 每处理1000个事件显示进度
        if mod(timing_info.processed_events, 1000) == 0
            fprintf('已处理 %d/%d 事件 (CLKH:%d, CLKS:%d, 数字校正:%d)\n', ...
                timing_info.processed_events, event_system.num_events, ...
                timing_info.clkh_events, timing_info.clks_events, timing_info.digital_corrections);
        end
    end
    
    % 统计有效输出
    timing_info.valid_outputs = sum(abs(adc_output) > 0.001);
    
    fprintf('增强事件处理完成: %d个事件, %d个有效输出 (%.1f%%)\n', ...
        timing_info.processed_events, timing_info.valid_outputs, ...
        100*timing_info.valid_outputs/num_samples);
end

function pipeline_state = initialize_enhanced_pipeline_state(num_stages, timing_control)
%INITIALIZE_ENHANCED_PIPELINE_STATE 初始化增强的流水线状态机
%   根据时钟分配方案为每一级创建状态缓存

    pipeline_state = struct();
    
    % SHA状态 (采样态CLKH, 保持态CLKS)
    pipeline_state.sha = struct(...
        'input_data', 0, ...
        'latched_data', 0, ...
        'output_data', 0, ...
        'state', 'idle', ...        % 'sampling', 'holding', 'idle'
        'valid', false, ...
        'clock_config', timing_control.clock_assignment.sha);
    
    % 流水线级状态
    for stage = 1:num_stages
        stage_field = ['stage_', num2str(stage)];
        clock_config = timing_control.clock_assignment.(stage_field);
        
        pipeline_state.(stage_field) = struct(...
            'input_data', 0, ...
            'latched_data', 0, ...
            'analog_output', 0, ...
            'digital_output', [0, 0], ...
            'state', 'idle', ...        % 'sampling', 'holding', 'idle'
            'valid_analog', false, ...
            'valid_digital', false, ...
            'clock_config', clock_config);
    end
    
    % Flash ADC状态 (采样态CLKS, 保持态CLKH)
    pipeline_state.flash = struct(...
        'input_data', 0, ...
        'latched_data', 0, ...
        'digital_output', [0, 0], ...
        'state', 'idle', ...
        'valid', false, ...
        'delay_counter', 0, ...     % 延迟4.5个周期计数器
        'clock_config', timing_control.clock_assignment.flash);
end

function digital_correction_buffers = initialize_digital_correction_buffers(num_stages, timing_control)
%INITIALIZE_DIGITAL_CORRECTION_BUFFERS 初始化数字校准延迟缓冲
%   实现精确的延迟同步: STAGE1延迟4个周期, STAGE2延迟3.5个周期等

    digital_correction_buffers = struct();
    
    % 为每一级创建延迟缓冲
    for stage = 1:num_stages
        delay_cycles = timing_control.digital_correction_delays(stage);
        buffer_depth = ceil(delay_cycles * 2);  % 转换为半周期单位
        
        digital_correction_buffers.(['stage_', num2str(stage)]) = struct(...
            'digital_buffer', zeros(buffer_depth, 2), ...
            'valid_buffer', false(buffer_depth, 1), ...
            'write_index', 1, ...
            'read_index', 1, ...
            'delay_cycles', delay_cycles, ...
            'buffer_depth', buffer_depth);
    end
    
    % Flash ADC延迟缓冲 (延迟0.5个周期)
    flash_delay_cycles = timing_control.digital_correction_delays(num_stages + 1);
    flash_buffer_depth = ceil(flash_delay_cycles * 2);
    
    digital_correction_buffers.flash = struct(...
        'digital_buffer', zeros(flash_buffer_depth, 2), ...
        'valid_buffer', false(flash_buffer_depth, 1), ...
        'write_index', 1, ...
        'read_index', 1, ...
        'delay_cycles', flash_delay_cycles, ...
        'buffer_depth', flash_buffer_depth);
    
    fprintf('数字校准延迟缓冲初始化完成\n');
end

function stage_history = initialize_enhanced_stage_history(num_samples, num_stages)
%INITIALIZE_ENHANCED_STAGE_HISTORY 初始化增强的各级历史数据存储

    stage_history = struct();
    stage_history.sha = zeros(num_samples, 1);
    
    for stage = 1:num_stages
        stage_history.(['stage_', num2str(stage), '_analog']) = zeros(num_samples, 1);
        stage_history.(['stage_', num2str(stage), '_digital']) = zeros(num_samples, 2);
    end
    
    stage_history.flash = zeros(num_samples, 2);
    stage_history.valid_mask = false(num_samples, 1);
    
    % 增强的历史数据字段
    stage_history.timing_accuracy = zeros(num_samples, 1);
    stage_history.delay_alignment_status = false(num_samples, 1);
    stage_history.digital_correction_status = false(num_samples, 1);
end

function [pipeline_state, stage_history, timing_info] = process_clkh_rising_enhanced(...
    pipeline_state, stage_history, timing_info, sample_idx, timestamp, timing_control, params)
%PROCESS_CLKH_RISING_ENHANCED CLKH上升沿处理 (增强版)
%   SHA保持态, 偶数级保持态

    num_stages = params.num_stages;

    % 1. SHA进入保持态 (时钟上升沿后输出锁存的电压值)
    if strcmp(pipeline_state.sha.state, 'sampling')
        pipeline_state.sha.output_data = pipeline_state.sha.latched_data;
        pipeline_state.sha.state = 'holding';
        pipeline_state.sha.valid = true;

        % 记录到历史
        stage_history.sha(sample_idx) = pipeline_state.sha.output_data;
        timing_info.sha_updates = timing_info.sha_updates + 1;
    end

    % 2. 偶数级 (STAGE2,4,6,8) 进入保持态
    for stage = 2:2:num_stages
        stage_field = ['stage_', num2str(stage)];

        if strcmp(pipeline_state.(stage_field).state, 'sampling')
            % 执行1.5bit ADC + MDAC处理
            [analog_out, digital_out] = process_1p5bit_pipeline_stage(...
                pipeline_state.(stage_field).latched_data, stage, params);

            % 存储结果
            pipeline_state.(stage_field).analog_output = analog_out;
            pipeline_state.(stage_field).digital_output = digital_out;
            pipeline_state.(stage_field).state = 'holding';
            pipeline_state.(stage_field).valid_analog = true;
            pipeline_state.(stage_field).valid_digital = true;

            % 记录到历史
            stage_history.(['stage_', num2str(stage), '_analog'])(sample_idx) = analog_out;
            stage_history.(['stage_', num2str(stage), '_digital'])(sample_idx, :) = digital_out;
            timing_info.pipeline_updates = timing_info.pipeline_updates + 1;
        end
    end
end

function [pipeline_state, stage_history, timing_info] = process_clkh_falling_enhanced(...
    pipeline_state, stage_history, timing_info, input_value, sample_idx, timestamp, ...
    rel_error_params, timing_control, params)
%PROCESS_CLKH_FALLING_ENHANCED CLKH下降沿处理 (增强版)
%   SHA采样并锁存, 偶数级采样并锁存

    num_stages = params.num_stages;

    % 1. SHA采样态 (时钟下降沿前更新并锁存输入信号，输出0V)
    pipeline_state.sha.state = 'sampling';
    pipeline_state.sha.input_data = input_value;
    pipeline_state.sha.latched_data = input_value;  % 锁存输入信号
    % 注意: 采样态时输出为0V，在上升沿时才输出锁存值

    % 2. 偶数级 (STAGE2,4,6,8) 采样态
    for stage = 2:2:num_stages
        stage_field = ['stage_', num2str(stage)];
        prev_stage_field = ['stage_', num2str(stage-1)];

        % 从前一级获取数据 (前一级应该处于保持态)
        if pipeline_state.(prev_stage_field).valid_analog && ...
           strcmp(pipeline_state.(prev_stage_field).state, 'holding')

            pipeline_state.(stage_field).state = 'sampling';
            pipeline_state.(stage_field).input_data = pipeline_state.(prev_stage_field).analog_output;
            pipeline_state.(stage_field).latched_data = pipeline_state.(stage_field).input_data;
        end
    end
end

function [pipeline_state, stage_history, timing_info] = process_clks_rising_enhanced(...
    pipeline_state, stage_history, timing_info, sample_idx, timestamp, timing_control, params)
%PROCESS_CLKS_RISING_ENHANCED CLKS上升沿处理 (增强版)
%   奇数级保持态, Flash ADC保持态

    num_stages = params.num_stages;

    % 1. STAGE1从SHA获取数据并进入保持态
    if pipeline_state.sha.valid && strcmp(pipeline_state.sha.state, 'holding')
        if strcmp(pipeline_state.stage_1.state, 'sampling')
            % 执行1.5bit ADC + MDAC处理
            [analog_out, digital_out] = process_1p5bit_pipeline_stage(...
                pipeline_state.stage_1.latched_data, 1, params);

            % 存储结果
            pipeline_state.stage_1.analog_output = analog_out;
            pipeline_state.stage_1.digital_output = digital_out;
            pipeline_state.stage_1.state = 'holding';
            pipeline_state.stage_1.valid_analog = true;
            pipeline_state.stage_1.valid_digital = true;

            % 记录到历史
            stage_history.stage_1_analog(sample_idx) = analog_out;
            stage_history.stage_1_digital(sample_idx, :) = digital_out;
            timing_info.pipeline_updates = timing_info.pipeline_updates + 1;
        end
    end

    % 2. 其他奇数级 (STAGE3,5,7) 进入保持态
    for stage = 3:2:num_stages
        stage_field = ['stage_', num2str(stage)];

        if strcmp(pipeline_state.(stage_field).state, 'sampling')
            % 执行1.5bit ADC + MDAC处理
            [analog_out, digital_out] = process_1p5bit_pipeline_stage(...
                pipeline_state.(stage_field).latched_data, stage, params);

            % 存储结果
            pipeline_state.(stage_field).analog_output = analog_out;
            pipeline_state.(stage_field).digital_output = digital_out;
            pipeline_state.(stage_field).state = 'holding';
            pipeline_state.(stage_field).valid_analog = true;
            pipeline_state.(stage_field).valid_digital = true;

            % 记录到历史
            stage_history.(['stage_', num2str(stage), '_analog'])(sample_idx) = analog_out;
            stage_history.(['stage_', num2str(stage), '_digital'])(sample_idx, :) = digital_out;
            timing_info.pipeline_updates = timing_info.pipeline_updates + 1;
        end
    end

    % 3. Flash ADC进入保持态 (需要检查延迟计数器)
    if pipeline_state.flash.delay_counter >= 9  % 4.5个周期 = 9个半周期
        if strcmp(pipeline_state.flash.state, 'sampling')
            % 执行2bit Flash ADC量化
            flash_digital = process_2bit_flash_adc(pipeline_state.flash.latched_data, params);

            pipeline_state.flash.digital_output = flash_digital;
            pipeline_state.flash.state = 'holding';
            pipeline_state.flash.valid = true;

            % 记录到历史
            stage_history.flash(sample_idx, :) = flash_digital;
            timing_info.flash_updates = timing_info.flash_updates + 1;
        end
    end
end

function [pipeline_state, stage_history, adc_output, binary_output, timing_info, digital_correction_buffers] = ...
    process_clks_falling_enhanced(...
    pipeline_state, stage_history, adc_output, binary_output, timing_info, ...
    digital_correction_buffers, sample_idx, timestamp, rel_error_params, timing_control, params)
%PROCESS_CLKS_FALLING_ENHANCED CLKS下降沿处理 (增强版)
%   奇数级采样并锁存, Flash ADC采样并锁存, 执行数字校正

    num_stages = params.num_stages;

    % 1. STAGE1从SHA采样
    if pipeline_state.sha.valid && strcmp(pipeline_state.sha.state, 'holding')
        pipeline_state.stage_1.state = 'sampling';
        pipeline_state.stage_1.input_data = pipeline_state.sha.output_data;
        pipeline_state.stage_1.latched_data = pipeline_state.stage_1.input_data;
    end

    % 2. 其他奇数级 (STAGE3,5,7) 采样态
    for stage = 3:2:num_stages
        stage_field = ['stage_', num2str(stage)];
        prev_stage_field = ['stage_', num2str(stage-1)];

        % 从前一级获取数据 (前一级应该处于保持态)
        if pipeline_state.(prev_stage_field).valid_analog && ...
           strcmp(pipeline_state.(prev_stage_field).state, 'holding')

            pipeline_state.(stage_field).state = 'sampling';
            pipeline_state.(stage_field).input_data = pipeline_state.(prev_stage_field).analog_output;
            pipeline_state.(stage_field).latched_data = pipeline_state.(stage_field).input_data;
        end
    end

    % 3. Flash ADC采样 (需要检查延迟)
    last_stage_field = ['stage_', num2str(num_stages)];
    if pipeline_state.(last_stage_field).valid_analog && ...
       strcmp(pipeline_state.(last_stage_field).state, 'holding')

        % 增加延迟计数器
        pipeline_state.flash.delay_counter = pipeline_state.flash.delay_counter + 1;

        % 检查是否达到4.5个周期延迟
        if pipeline_state.flash.delay_counter >= 9  % 4.5个周期 = 9个半周期
            pipeline_state.flash.state = 'sampling';
            pipeline_state.flash.input_data = pipeline_state.(last_stage_field).analog_output;
            pipeline_state.flash.latched_data = pipeline_state.flash.input_data;
        end
    end

    % 4. 执行数字校正 (如果所有级都准备好)
    if all_stages_ready_for_digital_correction(pipeline_state, num_stages)
        % 收集各级数字数据并写入延迟缓冲
        digital_correction_buffers = update_digital_correction_buffers(...
            digital_correction_buffers, pipeline_state, num_stages);

        % 尝试从延迟缓冲读取对齐的数据进行校正
        [analog_result, binary_result, correction_success] = ...
            perform_enhanced_digital_correction(...
            digital_correction_buffers, timing_control, params, timestamp);

        if correction_success
            adc_output(sample_idx) = analog_result;
            binary_output(sample_idx, :) = binary_result;
            timing_info.digital_corrections = timing_info.digital_corrections + 1;
            stage_history.digital_correction_status(sample_idx) = true;
        end
    end
end

function [analog_out, digital_out] = process_1p5bit_pipeline_stage(input_value, stage_num, params)
%PROCESS_1P5BIT_PIPELINE_STAGE 处理1.5bit流水线级
%   实现阈值[-1/4Vref, 1/4Vref]的1.5bit量化和MDAC传递函数

    Vref = params.Vref;

    % 1.5bit ADC量化 (阈值: ±Vref/4)
    threshold_low = -Vref/4;
    threshold_high = Vref/4;

    if input_value <= threshold_low
        digital_out = [0, 0];  % 00 -> D=-1
        D_value = -1;
    elseif input_value >= threshold_high
        digital_out = [1, 0];  % 10 -> D=+1
        D_value = 1;
    else
        digital_out = [0, 1];  % 01 -> D=0
        D_value = 0;
    end

    % MDAC传递函数: Vres = (1+a)*(2+b)*Vin - (1+a)*(1+b)*D*Vref
    % 理想状态下 a=0, b=0
    a = 0;  % 第一个误差参数
    b = 0;  % 第二个误差参数

    % 应用传递函数
    analog_out = (1+a)*(2+b)*input_value - (1+a)*(1+b)*D_value*Vref;

    % 限制输出范围
    analog_out = max(-Vref, min(Vref, analog_out));
end

function flash_digital = process_2bit_flash_adc(input_value, params)
%PROCESS_2BIT_FLASH_ADC 处理2bit Flash ADC
%   实现完整的2bit量化，阈值[-Vref/2, 0, +Vref/2]

    Vref = params.Vref;

    % 2bit Flash ADC的量化逻辑
    % 信号 < -1/2Vref → 00
    % -1/2Vref ≤ 信号 < 0 → 01
    % 0 ≤ 信号 < 1/2Vref → 10
    % 信号 ≥ 1/2Vref → 11

    if input_value < -Vref/2
        flash_digital = [0, 0];      % 00
    elseif input_value < 0
        flash_digital = [0, 1];      % 01
    elseif input_value < Vref/2
        flash_digital = [1, 0];      % 10
    else
        flash_digital = [1, 1];      % 11
    end
end

function ready = all_stages_ready_for_digital_correction(pipeline_state, num_stages)
%ALL_STAGES_READY_FOR_DIGITAL_CORRECTION 检查是否所有级准备好进行数字校正

    ready = pipeline_state.flash.valid;

    for stage = 1:num_stages
        stage_field = ['stage_', num2str(stage)];
        ready = ready && pipeline_state.(stage_field).valid_digital;
    end
end

% 导入数字校正函数
% 这些函数在 enhanced_digital_correction.m 文件中定义

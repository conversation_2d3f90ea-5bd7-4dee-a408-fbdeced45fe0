function [adc_output, binary_output, stage_history, timing_info] = event_driven_pipeline_adc_v2(Vin_p, Vin_n, params, rel_error_params, t_sampled, clks_input, clkh_input)
%EVENT_DRIVEN_PIPELINE_ADC_V2 事件驱动流水线ADC主模型 (v2版本)
%   专注于理想流水线ADC的零误差输出正确性验证
%   根据详细技术规范实现完整的流水线ADC模型
%
%   版本特点 (v2):
%   - 专注理想条件下的零误差验证
%   - 优化事件驱动时序控制
%   - 增强数字校正算法稳定性
%   - 改进与理想ADC的对比分析
%
%   技术规范要求:
%   1. 输入信号: 频率=23/1024*100e6 Hz，30个周期，相干采样
%   2. 模型输出: 两种模拟输出 + 理想ADC对比基准
%   3. 时序控制: 非交叠时钟(CLKH,CLKS)，每级延迟T/2
%   4. SHA电路: 时钟下降沿锁存，上升沿保持输出
%   5. 1.5bit流水线级: 阈值[-1/4Vref, 1/4Vref]，传递函数实现
%   6. Flash ADC: 2bit完整量化，延迟4.5个周期
%   7. 数字校准: 18bit→10bit全加器累加，理想DAC转换
%
%   输入参数:
%       Vin_p, Vin_n - 差分输入信号
%       params - ADC参数结构体
%       rel_error_params - 相对误差参数矩阵 (理想验证时全零)
%       t_sampled - 时间向量
%       clks_input, clkh_input - 非交叠时钟信号
%
%   输出参数:
%       adc_output - 最终DAC转换的模拟信号输出(模拟输出2)
%       binary_output - 10bit有效数字输出
%       stage_history - 各级历史数据(包含模拟输出1)
%       timing_info - 时序信息和性能统计
%
%   版本: v2.0 - 专注理想验证的优化版本

    % 参数验证和初始化
    [Vin_p, Vin_n, t_sampled, clks_input, clkh_input] = validate_inputs_v2(...
        Vin_p, Vin_n, t_sampled, clks_input, clkh_input);

    num_samples = length(t_sampled);
    num_stages = params.num_stages;

    if nargin < 4 || isempty(rel_error_params)
        % 默认为理想条件（零误差）
        rel_error_params = zeros(num_stages, 2);
        fprintf('使用理想条件：零误差参数 (%dx%d)\n', size(rel_error_params));
    end

    fprintf('=== 事件驱动流水线ADC v2.0 ===\n');
    fprintf('专注理想验证: 输入频率=%.3fMHz, %d个周期, %d级流水线\n', ...
            params.f_in/1e6, params.num_cycles, num_stages);
    fprintf('时序控制: 非交叠时钟(CLKH,CLKS), 每级延迟T/2\n');
    fprintf('样本数: %d, 时间范围: [%.6f, %.6f]s\n', ...
            num_samples, min(t_sampled), max(t_sampled));

    % 验证输入频率是否符合相干采样要求
    expected_freq = 23/1024*100e6;
    if abs(params.f_in - expected_freq) > 1e3
        warning('输入频率 %.3f MHz 偏离相干采样要求 %.3f MHz', ...
                params.f_in/1e6, expected_freq/1e6);
    end

    % 初始化时序控制系统
    timing_control = initialize_timing_control_system_v2(num_stages, params);

    % 初始化事件队列系统
    event_system = initialize_event_system_v2(t_sampled, clks_input, clkh_input, params);

    % 验证事件系统有效性
    if event_system.num_events == 0
        error('未检测到有效的时钟事件，无法运行事件驱动ADC模型');
    end

    % 初始化输出存储结构
    [stage_history, adc_output, binary_output] = initialize_output_storage_v2(num_samples, num_stages);

    % 主事件驱动处理循环
    fprintf('开始事件驱动处理...\n');

    % 调用优化的事件处理核心v3
    [adc_output, binary_output, stage_history, timing_info] = ...
        event_processing_core_v3(Vin_p, Vin_n, t_sampled, event_system, ...
                                timing_control, rel_error_params, params);

    fprintf('事件驱动流水线ADC v2.0 处理完成\n');
    fprintf('有效输出样本: %d/%d (%.1f%%)\n', ...
            sum(abs(adc_output) > 0.001), num_samples, ...
            100*sum(abs(adc_output) > 0.001)/num_samples);
end

function timing_control = initialize_timing_control_system_v2(num_stages, params)
%INITIALIZE_TIMING_CONTROL_SYSTEM_V2 初始化时序控制系统 (v2版本)

    timing_control = struct();

    % 时钟分配方案 (根据技术规范)
    % SHA:      采样态(CLKH) → 保持态(CLKS)
    % STAGE1:   采样态(CLKS) → 保持态(CLKH)
    % STAGE2:   采样态(CLKH) → 保持态(CLKS)
    % ...
    % STAGE8:   采样态(CLKH) → 保持态(CLKS)
    % Flash ADC: 采样态(CLKS) → 保持态(CLKH)

    timing_control.clock_assignment = struct();
    timing_control.clock_assignment.sha = struct('sample_clk', 'CLKH', 'hold_clk', 'CLKS');

    for stage = 1:num_stages
        if mod(stage, 2) == 1  % 奇数级 (STAGE1,3,5,7)
            timing_control.clock_assignment.(['stage_', num2str(stage)]) = ...
                struct('sample_clk', 'CLKS', 'hold_clk', 'CLKH');
        else  % 偶数级 (STAGE2,4,6,8)
            timing_control.clock_assignment.(['stage_', num2str(stage)]) = ...
                struct('sample_clk', 'CLKH', 'hold_clk', 'CLKS');
        end
    end

    timing_control.clock_assignment.flash = struct('sample_clk', 'CLKS', 'hold_clk', 'CLKH');

    % 延迟管理配置 (每级相对前级延迟T/2)
    timing_control.delay_config = struct();
    timing_control.delay_config.half_period = 1 / (2 * params.fs);  % T/2

    % 数字校准延迟同步配置 (v2优化)
    % STAGE1输出延迟4个周期, STAGE2延迟3.5个周期, ..., STAGE8延迟0.5个周期
    timing_control.digital_correction_delays = zeros(num_stages + 1, 1);  % +1 for Flash
    for stage = 1:num_stages
        timing_control.digital_correction_delays(stage) = (num_stages - stage + 1) * 0.5;
    end
    timing_control.digital_correction_delays(num_stages + 1) = 0.5;  % Flash ADC延迟0.5个周期

    fprintf('时序控制系统v2初始化完成\n');
end

function event_system = initialize_event_system_v2(t_sampled, clks_input, clkh_input, params)
%INITIALIZE_EVENT_SYSTEM_V2 初始化事件系统 (v2版本)

    event_system = struct();
    events = [];

    % 检测CLKS时钟边沿
    clks_rising = find(diff([0; clks_input]) > 0.5);
    clks_falling = find(diff([clks_input; 0]) > 0.5);

    % 检测CLKH时钟边沿
    clkh_rising = find(diff([0; clkh_input]) > 0.5);
    clkh_falling = find(diff([clkh_input; 0]) > 0.5);

    % 生成CLKS事件
    for i = 1:length(clks_rising)
        events = [events; struct('time', t_sampled(clks_rising(i)), ...
                                'index', clks_rising(i), ...
                                'type', 'CLKS_RISING', ...
                                'clock', 'CLKS')];
    end

    for i = 1:length(clks_falling)
        events = [events; struct('time', t_sampled(clks_falling(i)), ...
                                'index', clks_falling(i), ...
                                'type', 'CLKS_FALLING', ...
                                'clock', 'CLKS')];
    end

    % 生成CLKH事件
    for i = 1:length(clkh_rising)
        events = [events; struct('time', t_sampled(clkh_rising(i)), ...
                                'index', clkh_rising(i), ...
                                'type', 'CLKH_RISING', ...
                                'clock', 'CLKH')];
    end

    for i = 1:length(clkh_falling)
        events = [events; struct('time', t_sampled(clkh_falling(i)), ...
                                'index', clkh_falling(i), ...
                                'type', 'CLKH_FALLING', ...
                                'clock', 'CLKH')];
    end

    % 按时间排序事件
    if ~isempty(events)
        [~, sort_idx] = sort([events.time]);
        event_system.events = events(sort_idx);
    else
        event_system.events = [];
    end
    event_system.num_events = length(events);

    fprintf('事件系统v2初始化完成: %d个事件\n', event_system.num_events);
    fprintf('  CLKS上升沿: %d个, CLKS下降沿: %d个\n', length(clks_rising), length(clks_falling));
    fprintf('  CLKH上升沿: %d个, CLKH下降沿: %d个\n', length(clkh_rising), length(clkh_falling));
end

function [stage_history, adc_output, binary_output] = initialize_output_storage_v2(num_samples, num_stages)
%INITIALIZE_OUTPUT_STORAGE_V2 初始化输出存储 (v2版本)

    stage_history = struct();
    stage_history.sha = zeros(num_samples, 1);

    for stage = 1:num_stages
        stage_history.(['stage_', num2str(stage), '_analog']) = zeros(num_samples, 1);
        stage_history.(['stage_', num2str(stage), '_digital']) = zeros(num_samples, 2);
    end

    stage_history.flash = zeros(num_samples, 2);
    stage_history.valid_mask = false(num_samples, 1);

    % v2版本增强字段
    stage_history.timing_accuracy = zeros(num_samples, 1);
    stage_history.delay_alignment_status = false(num_samples, 1);
    stage_history.digital_correction_status = false(num_samples, 1);

    adc_output = zeros(num_samples, 1);
    binary_output = zeros(num_samples, 10);
end

function [Vin_p, Vin_n, t_sampled, clks_input, clkh_input] = validate_inputs_v2(...
    Vin_p, Vin_n, t_sampled, clks_input, clkh_input)
%VALIDATE_INPUTS_V2 验证和格式化输入参数 (v2版本)

    % 确保所有输入都是列向量
    Vin_p = Vin_p(:);
    Vin_n = Vin_n(:);
    t_sampled = t_sampled(:);
    clks_input = clks_input(:);
    clkh_input = clkh_input(:);

    % 验证输入长度一致性
    lengths = [length(Vin_p), length(Vin_n), length(t_sampled), ...
               length(clks_input), length(clkh_input)];

    if length(unique(lengths)) > 1
        error('所有输入向量的长度必须一致。当前长度: %s', mat2str(lengths));
    end

    % 验证时钟信号
    if all(clks_input == 0) || all(clks_input == clks_input(1))
        error('CLKS时钟信号无效或无变化');
    end

    if all(clkh_input == 0) || all(clkh_input == clkh_input(1))
        error('CLKH时钟信号无效或无变化');
    end

    % 检查非交叠时钟条件
    overlap_samples = sum((clks_input > 0.5) & (clkh_input > 0.5));
    total_samples = length(clks_input);

    if overlap_samples > 0.01 * total_samples  % 允许1%的重叠容差
        warning('检测到时钟重叠: %d个样本 (%.2f%%), 可能违反非交叠时钟要求', ...
                overlap_samples, 100*overlap_samples/total_samples);
    end

    fprintf('输入参数验证v2完成: %d个样本, 时钟重叠率: %.3f%%\n', ...
            total_samples, 100*overlap_samples/total_samples);
end 
function [t, sine_wave, clks_edges, clkh_edges] = generate_adaptive_timebase_v2(params)
%GENERATE_ADAPTIVE_TIMEBASE_V2 生成边沿触发时钟信号 (v2版本)
%   专为事件驱动核心V4设计，生成边沿有效信号替代传统0/1时钟
%   
%   VERSION HISTORY:
%   v1: 原始版本 - 传统0/1数字时钟信号生成
%   v2: 边沿触发优化版本 - 边沿位置标记为true，其余为false
%
%   v2版本特点:
%   - 生成边沿有效信号：上升/下降沿位置为true，其余时间为false
%   - 支持独立的CLKS和CLKH边沿检测
%   - 优化非交叠时钟边沿生成
%   - 精确的边沿时序控制
%
%   输入参数:
%       params - ADC参数结构体
%           fs - ADC采样频率 (Hz)
%           f_in - 输入正弦波频率 (Hz)
%           Vref - 参考电压 (V)
%           num_sine_cycles - 仿真正弦波周期数 (默认30)
%
%   输出参数:
%       t - 高精度时间向量
%       sine_wave - 正弦波信号
%       clks_edges - CLKS边沿信号结构体
%           .rising - CLKS上升沿位置 (logical)
%           .falling - CLKS下降沿位置 (logical)
%       clkh_edges - CLKH边沿信号结构体  
%           .rising - CLKH上升沿位置 (logical)
%           .falling - CLKH下降沿位置 (logical)

%% 参数设置和初始化
fs_adc = params.fs;                    % ADC采样率
f_clk = fs_adc;                        % 时钟频率 
f_sin = params.f_in;                   % 正弦波频率
A_sin = 1 * params.Vref;               % 正弦波振幅

% 仿真时长
if isfield(params, 'num_sine_cycles')
    num_cycles = params.num_sine_cycles;
else
    num_cycles = 30;
end
t_sim = num_cycles / f_sin;           % 仿真时长

% V2版本：优化的时钟参数设置（非交叠时钟）
clks_width = 0.48/fs_adc;             % CLKS脉冲宽度: 48%占空比
clks_delay = 0.5e-9;                  % CLKS相位延迟: 0.5ns
clkh_width = 0.48/fs_adc;             % CLKH脉冲宽度: 48%占空比
clkh_delay = 5.5e-9;                  % CLKH相位延迟: 5.5ns (确保非交叠)

fprintf('=== 边沿触发时钟信号生成器 v2.0 ===\n');
fprintf('时钟频率: %.1f MHz, 仿真时长: %.3f μs\n', f_clk/1e6, t_sim*1e6);
fprintf('CLKS延迟: %.1f ns, 脉宽: %.1f ns\n', clks_delay*1e9, clks_width*1e9);
fprintf('CLKH延迟: %.1f ns, 脉宽: %.1f ns\n', clkh_delay*1e9, clkh_width*1e9);

%% V2版本：精确的边沿时间点计算
clks_edge_times = [];  % CLKS边沿时间集合
clkh_edge_times = [];  % CLKH边沿时间集合

num_clk_cycles = ceil(t_sim * f_clk) + 2;  % 增加缓冲周期

% 计算CLKS边沿时间
for n = 0:num_clk_cycles
    % CLKS上升沿和下降沿
    clks_rising_time = n/f_clk + clks_delay;
    clks_falling_time = clks_rising_time + clks_width;
    
    if clks_rising_time >= 0 && clks_rising_time <= t_sim
        clks_edge_times = [clks_edge_times, clks_rising_time];
    end
    if clks_falling_time >= 0 && clks_falling_time <= t_sim
        clks_edge_times = [clks_edge_times, clks_falling_time];
    end
end

% 计算CLKH边沿时间
for n = 0:num_clk_cycles
    % CLKH上升沿和下降沿
    clkh_rising_time = n/f_clk + clkh_delay;
    clkh_falling_time = clkh_rising_time + clkh_width;
    
    if clkh_rising_time >= 0 && clkh_rising_time <= t_sim
        clkh_edge_times = [clkh_edge_times, clkh_rising_time];
    end
    if clkh_falling_time >= 0 && clkh_falling_time <= t_sim
        clkh_edge_times = [clkh_edge_times, clkh_falling_time];
    end
end

% 正弦波特征点
t_sin_features = 0 : 1/(16*f_sin) : t_sim;  % 增加正弦波采样点

%% V2版本：优化的自适应时间向量生成
% 合并所有关键时间点
all_key_points = unique(sort([clks_edge_times, clkh_edge_times, t_sin_features]));
all_key_points = all_key_points(all_key_points <= t_sim);

% 自适应插值生成高精度时间向量
t = [];
edge_time_tolerance = 0.05e-9;        % 边沿时间容差: 0.05ns
max_step_limit = 0.08e-9;             % 最大时间步长: 0.08ns

for k = 1:length(all_key_points)-1
    interval = all_key_points(k+1) - all_key_points(k);
    
    % V2版本：改进的自适应步长策略
    if interval > 2/(f_clk*100)        % 较长间隔：粗采样
        step = min([interval/8, 1/(50*f_sin), max_step_limit]);
    else                               % 短间隔（边沿附近）：细采样
        step = min([interval/20, 1/(2000*f_clk), edge_time_tolerance]);
    end
    
    % 步长边界限制
    min_step = 1/(20000*f_clk);        % 最小步长
    step = max(step, min_step);
    step = min(step, max_step_limit);
    
    % 生成区间内的时间点
    if step > 0
        t_interval = all_key_points(k) : step : all_key_points(k+1);
        t = [t, t_interval];
    end
end

% 添加最后一个关键点
if ~isempty(all_key_points)
    t = [t, all_key_points(end)];
end

% 去重并排序，确保单调性
t = unique(t);
t = t(t <= t_sim);

%% 生成高精度正弦波信号
sine_wave = A_sin * sin(2*pi*f_sin*t);

%% V2版本：生成边沿有效信号
% 初始化边沿检测结构体
clks_edges = struct();
clkh_edges = struct();

% 初始化边沿信号（全部为false）
clks_edges.rising = false(size(t));
clks_edges.falling = false(size(t));
clkh_edges.rising = false(size(t));
clkh_edges.falling = false(size(t));

edge_detection_tolerance = 1e-12;     % 边沿检测容差

%% 标记CLKS边沿
for n = 0:num_clk_cycles
    % CLKS上升沿
    clks_rising_time = n/f_clk + clks_delay;
    if clks_rising_time >= 0 && clks_rising_time <= t_sim
        % 找到最接近的时间点
        [~, closest_idx] = min(abs(t - clks_rising_time));
        if abs(t(closest_idx) - clks_rising_time) <= edge_detection_tolerance
            clks_edges.rising(closest_idx) = true;
        end
    end
    
    % CLKS下降沿
    clks_falling_time = clks_rising_time + clks_width;
    if clks_falling_time >= 0 && clks_falling_time <= t_sim
        [~, closest_idx] = min(abs(t - clks_falling_time));
        if abs(t(closest_idx) - clks_falling_time) <= edge_detection_tolerance
            clks_edges.falling(closest_idx) = true;
        end
    end
end

%% 标记CLKH边沿
for n = 0:num_clk_cycles
    % CLKH上升沿
    clkh_rising_time = n/f_clk + clkh_delay;
    if clkh_rising_time >= 0 && clkh_rising_time <= t_sim
        [~, closest_idx] = min(abs(t - clkh_rising_time));
        if abs(t(closest_idx) - clkh_rising_time) <= edge_detection_tolerance
            clkh_edges.rising(closest_idx) = true;
        end
    end
    
    % CLKH下降沿
    clkh_falling_time = clkh_rising_time + clkh_width;
    if clkh_falling_time >= 0 && clkh_falling_time <= t_sim
        [~, closest_idx] = min(abs(t - clkh_falling_time));
        if abs(t(closest_idx) - clkh_falling_time) <= edge_detection_tolerance
            clkh_edges.falling(closest_idx) = true;
        end
    end
end

%% 确保输出格式一致性
t = t(:);                              % 列向量
sine_wave = sine_wave(:);              % 列向量
clks_edges.rising = clks_edges.rising(:);
clks_edges.falling = clks_edges.falling(:);
clkh_edges.rising = clkh_edges.rising(:);
clkh_edges.falling = clkh_edges.falling(:);

%% V2版本：边沿信号验证和统计
clks_rising_count = sum(clks_edges.rising);
clks_falling_count = sum(clks_edges.falling);
clkh_rising_count = sum(clkh_edges.rising);
clkh_falling_count = sum(clkh_edges.falling);

% 验证非交叠时钟条件
overlap_check = any((clks_edges.rising | clks_edges.falling) & ...
                   (clkh_edges.rising | clkh_edges.falling));

fprintf('边沿信号生成完成:\n');
fprintf('  总时间点数: %d\n', length(t));
fprintf('  CLKS上升沿: %d个, CLKS下降沿: %d个\n', clks_rising_count, clks_falling_count);
fprintf('  CLKH上升沿: %d个, CLKH下降沿: %d个\n', clkh_rising_count, clkh_falling_count);
if overlap_check
    fprintf('  非交叠验证: %s\n', '失败');
else
    fprintf('  非交叠验证: %s\n', '通过');
end

if overlap_check
    warning('检测到时钟边沿重叠，可能影响事件驱动处理');
end

end 
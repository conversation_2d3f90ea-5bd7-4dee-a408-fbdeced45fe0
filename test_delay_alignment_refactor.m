%TEST_DELAY_ALIGNMENT_REFACTOR 测试延迟对齐重构功能
%   验证延迟对齐从数字校正模块迁移到事件驱动核心后的功能正确性

function test_delay_alignment_refactor()
    fprintf('=== 延迟对齐重构功能测试 ===\n');
    
    try
        %% 第一步：初始化测试参数
        fprintf('\n[步骤1/4] 初始化测试参数...\n');
        
        % 创建基本ADC参数
        params = struct();
        params.fs = 100e6;                    % 100 MHz采样率
        params.num_stages = 8;                % 8级流水线
        params.Vref = 1.0;                    % 1V参考电压
        params.f_in = 23/1024*100e6;          % 输入频率
        params.num_cycles = 30;               % 30个周期
        params.time_step = 1e-11;             % 10ps时间步长
        
        % 创建零误差参数（理想测试）
        rel_error_params = zeros(8, 3);
        
        fprintf('✓ 参数初始化完成\n');
        
        %% 第二步：生成测试信号
        fprintf('\n[步骤2/4] 生成测试信号...\n');
        
        % 生成时间基准和数据结构体
        [t, sine_wave, clks, clkh, digital_data_struct, analog_data_struct] = generate_timebase(params);
        
        % 创建差分输入信号
        Vin_p = sine_wave;
        Vin_n = zeros(size(sine_wave));
        
        num_samples = length(t);
        fprintf('✓ 生成%d个时间点的测试信号\n', num_samples);
        
        %% 第三步：测试重构后的事件驱动核心
        fprintf('\n[步骤3/4] 测试重构后的事件驱动核心...\n');
        
        % 调用重构后的事件驱动核心（包含延迟对齐）
        [digital_data_struct_processed, analog_data_struct_processed, processing_info] = ...
            event_processing_core_v6(Vin_p, Vin_n, t, clks, clkh, ...
                                    digital_data_struct, analog_data_struct, ...
                                    rel_error_params, params);
        
        % 验证延迟对齐是否启用
        if processing_info.delay_alignment_enabled
            fprintf('✓ 延迟对齐已启用\n');
            fprintf('  延迟步数: [%s]\n', sprintf('%d ', processing_info.delay_steps));
        else
            error('延迟对齐未启用');
        end
        
        % 检查数据输出
        digital_outputs = processing_info.digital_outputs;
        analog_outputs = processing_info.analog_outputs;
        
        fprintf('✓ 事件驱动处理完成\n');
        fprintf('  数字输出: %d\n', digital_outputs);
        fprintf('  模拟输出: %d\n', analog_outputs);
        
        %% 第四步：测试简化后的数字校正
        fprintf('\n[步骤4/4] 测试简化后的数字校正...\n');
        
        % 调用简化后的数字校正模块
        [corrected_analog_output, corrected_binary_output, correction_metrics] = ...
            digital_correction_v4(digital_data_struct_processed, analog_data_struct_processed, t, params);
        
        % 验证校正结果
        valid_outputs = correction_metrics.valid_outputs;
        processing_efficiency = correction_metrics.processing_efficiency;
        
        fprintf('✓ 数字校正完成\n');
        fprintf('  有效校正输出: %d/%d (%.1f%%)\n', ...
                valid_outputs, num_samples, processing_efficiency * 100);
        
        %% 第五步：验证结果质量
        fprintf('\n[验证] 检查结果质量...\n');
        
        % 检查输出范围
        max_output = max(abs(corrected_analog_output));
        min_output = min(abs(corrected_analog_output(corrected_analog_output ~= 0)));
        
        fprintf('  输出范围: %.6f V 到 %.6f V\n', min_output, max_output);
        
        % 检查是否有合理的输出
        if valid_outputs > 0 && max_output > 0.001
            fprintf('✓ 重构功能验证成功\n');
            fprintf('✓ 延迟对齐已成功迁移到事件驱动核心\n');
            fprintf('✓ 数字校正模块简化成功\n');
        else
            warning('输出质量可能存在问题');
        end
        
        %% 总结
        fprintf('\n=== 测试总结 ===\n');
        fprintf('✓ 延迟对齐重构: 成功\n');
        fprintf('✓ 事件驱动核心: 正常工作\n');
        fprintf('✓ 数字校正简化: 正常工作\n');
        fprintf('✓ 数据流重构: 完成\n');
        
    catch ME
        fprintf('✗ 测试失败: %s\n', ME.message);
        fprintf('  错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
        rethrow(ME);
    end
end

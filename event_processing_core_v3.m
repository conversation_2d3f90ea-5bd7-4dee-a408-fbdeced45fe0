function [adc_output, binary_output, stage_history, timing_info] = ...
    event_processing_core_v3(Vin_p, Vin_n, t_sampled, event_system, ...
                            timing_control, rel_error_params, params)
%EVENT_PROCESSING_CORE_V3 事件处理核心 (v3版本)
%   VERSION HISTORY:
%   v1: 原始版本 - 基础事件驱动处理功能
%   v2: 优化延迟缓冲机制和数字校正算法
%   v3: 简化时序控制逻辑，移除复杂延迟缓冲，提高输出有效性
%
%   v3版本主要改进:
%   1. 简化时序控制逻辑，避免复杂的延迟缓冲
%   2. 直接数据传递，减少状态同步错误
%   3. 优化数字校正算法，提高输出有效性
%   4. 增强数据有效性检测，降低同步要求
%
%   主要修复:
%   - 移除复杂的延迟缓冲机制
%   - 简化状态机逻辑
%   - 直接进行18bit到10bit转换
%   - 增强数据有效性检测

    num_samples = length(t_sampled);
    num_stages = params.num_stages;
    
    % 初始化输出
    adc_output = zeros(num_samples, 1);
    binary_output = zeros(num_samples, 10);
    stage_history = initialize_stage_history_v3(num_samples, num_stages);
    timing_info = struct('processed_events', 0, 'valid_outputs', 0, ...
                        'sha_updates', 0, 'pipeline_updates', 0, 'flash_updates', 0, ...
                        'processing_efficiency', 0, 'data_alignment_quality', 0);
    
    % 初始化流水线状态 (v3简化版)
    pipeline_state = initialize_pipeline_state_v3(num_stages, params);
    
    fprintf('事件处理核心v3启动: %d个事件待处理\n', event_system.num_events);
    
    % 主事件处理循环 (v3优化版)
    for event_idx = 1:event_system.num_events
        current_event = event_system.events(event_idx);
        
        % v3版本：直接处理事件，减少中间缓冲
        try
            pipeline_state = process_event_v3(current_event, pipeline_state, ...
                                             Vin_p, Vin_n, t_sampled, ...
                                             timing_control, rel_error_params, params);
            
            % 直接生成输出，避免复杂的延迟对齐
            if pipeline_state.ready_for_output
                [adc_output, binary_output, stage_history] = ...
                    generate_direct_output_v3(pipeline_state, current_event, ...
                                             adc_output, binary_output, stage_history);
                timing_info.valid_outputs = timing_info.valid_outputs + 1;
            end
            
            timing_info.processed_events = timing_info.processed_events + 1;
            
        catch event_error
            fprintf('事件处理错误 (索引 %d): %s\n', event_idx, event_error.message);
            continue;
        end
    end
    
    % 计算v3版本性能指标
    timing_info.processing_efficiency = timing_info.valid_outputs / max(1, timing_info.processed_events);
    timing_info.data_alignment_quality = sum(stage_history.valid_mask) / num_samples;
    
    fprintf('事件处理核心v3完成: %d/%d事件成功处理 (%.1f%%)\n', ...
            timing_info.processed_events, event_system.num_events, ...
            100*timing_info.processing_efficiency);
end

function stage_history = initialize_stage_history_v3(num_samples, num_stages)
%INITIALIZE_STAGE_HISTORY_V3 初始化级历史记录 (v3版本)
    
    stage_history = struct();
    stage_history.sha = zeros(num_samples, 1);
    
    for stage = 1:num_stages
        stage_history.(['stage_', num2str(stage), '_analog']) = zeros(num_samples, 1);
        stage_history.(['stage_', num2str(stage), '_digital']) = zeros(num_samples, 2);
    end
    
    stage_history.flash = zeros(num_samples, 2);
    stage_history.valid_mask = false(num_samples, 1);
    
    % v3版本简化字段
    stage_history.timing_accuracy = zeros(num_samples, 1);
    stage_history.output_quality = zeros(num_samples, 1);
end

function pipeline_state = initialize_pipeline_state_v3(num_stages, params)
%INITIALIZE_PIPELINE_STATE_V3 初始化流水线状态 (v3版本)
    
    pipeline_state = struct();
    pipeline_state.current_stage = 0;
    pipeline_state.ready_for_output = false;
    pipeline_state.data_valid = false;
    
    % v3版本：简化状态管理
    pipeline_state.sha_value = 0;
    pipeline_state.stage_values = zeros(num_stages, 1);
    pipeline_state.digital_codes = zeros(num_stages, 2);
    pipeline_state.flash_code = [0, 0];
    
    % 简化的处理状态
    pipeline_state.sha_ready = false;
    pipeline_state.stages_ready = false(num_stages, 1);
    pipeline_state.flash_ready = false;
end

function pipeline_state = process_event_v3(event, pipeline_state, ...
                                          Vin_p, Vin_n, t_sampled, ...
                                          timing_control, rel_error_params, params)
%PROCESS_EVENT_V3 处理单个事件 (v3版本)
    
    event_index = event.index;
    
    switch event.type
        case 'CLKH_RISING'
            % SHA采样操作
            if event_index <= length(Vin_p)
                pipeline_state.sha_value = Vin_p(event_index) - Vin_n(event_index);
                pipeline_state.sha_ready = true;
            end
            
        case 'CLKS_FALLING'
            % 触发流水线处理
            if pipeline_state.sha_ready
                pipeline_state = process_pipeline_v3(pipeline_state, ...
                                                    timing_control, rel_error_params, params);
            end
            
        case 'CLKH_FALLING'
            % 完成周期，准备输出
            if pipeline_state.stages_ready(end) && pipeline_state.flash_ready
                pipeline_state.ready_for_output = true;
                pipeline_state.data_valid = true;
            end
            
        otherwise
            % v3版本：忽略未识别事件
    end
end

function pipeline_state = process_pipeline_v3(pipeline_state, ...
                                             timing_control, rel_error_params, params)
%PROCESS_PIPELINE_V3 处理流水线级 (v3版本)
    
    num_stages = params.num_stages;
    input_value = pipeline_state.sha_value;
    
    % v3版本：直接顺序处理各级
    for stage = 1:num_stages
        % 简化的1.5bit级处理
        threshold = 0.25 * params.Vref;
        
        if input_value > threshold
            digital_code = [1, 0];  % +1
            residue = 2 * input_value - params.Vref;
        elseif input_value < -threshold
            digital_code = [0, 1];  % -1
            residue = 2 * input_value + params.Vref;
        else
            digital_code = [0, 0];  % 0
            residue = 2 * input_value;
        end
        
        % 应用相对误差（如果有）
        if size(rel_error_params, 1) >= stage
            gain_error = rel_error_params(stage, 1);
            residue = residue * (1 + gain_error);
        end
        
        % 存储结果
        pipeline_state.stage_values(stage) = residue;
        pipeline_state.digital_codes(stage, :) = digital_code;
        pipeline_state.stages_ready(stage) = true;
        
        % 下一级输入
        input_value = residue;
    end
    
    % Flash ADC处理 (2bit)
    final_value = input_value;
    if final_value > 0.5 * params.Vref
        flash_code = [1, 1];  % 3
    elseif final_value > 0
        flash_code = [1, 0];  % 2
    elseif final_value > -0.5 * params.Vref
        flash_code = [0, 1];  % 1
    else
        flash_code = [0, 0];  % 0
    end
    
    pipeline_state.flash_code = flash_code;
    pipeline_state.flash_ready = true;
end

function [adc_output, binary_output, stage_history] = ...
    generate_direct_output_v3(pipeline_state, event, ...
                             adc_output, binary_output, stage_history)
%GENERATE_DIRECT_OUTPUT_V3 直接生成输出 (v3版本)
    
    event_index = event.index;
    
    if event_index > length(adc_output)
        return;
    end
    
    % v3版本：简化的数字校正
    digital_sum = 0;
    weight = 256;  % 从最高位开始
    
    % 流水线级贡献
    for stage = 1:size(pipeline_state.digital_codes, 1)
        stage_code = pipeline_state.digital_codes(stage, :);
        if isequal(stage_code, [1, 0])
            digital_sum = digital_sum + weight;
        elseif isequal(stage_code, [0, 1])
            digital_sum = digital_sum - weight;
        end
        weight = weight / 2;
    end
    
    % Flash ADC贡献
    flash_value = pipeline_state.flash_code(1) * 2 + pipeline_state.flash_code(2);
    digital_sum = digital_sum + flash_value;
    
    % 转换为10位二进制
    digital_sum = max(0, min(1023, round(digital_sum + 512)));
    binary_10bit = de2bi(digital_sum, 10, 'left-msb');
    
    % 生成模拟输出
    analog_output = (digital_sum - 512) / 512 * 1.0;  % 归一化到[-1, 1]
    
    % 存储输出
    adc_output(event_index) = analog_output;
    binary_output(event_index, :) = binary_10bit;
    
    % 更新历史记录
    stage_history.sha(event_index) = pipeline_state.sha_value;
    for stage = 1:size(pipeline_state.stage_values, 1)
        stage_history.(['stage_', num2str(stage), '_analog'])(event_index) = ...
            pipeline_state.stage_values(stage);
        stage_history.(['stage_', num2str(stage), '_digital'])(event_index, :) = ...
            pipeline_state.digital_codes(stage, :);
    end
    stage_history.flash(event_index, :) = pipeline_state.flash_code;
    stage_history.valid_mask(event_index) = true;
    stage_history.output_quality(event_index) = 1.0;
end 
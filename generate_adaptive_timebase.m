function [t, sine_wave, clks, clkh] = generate_adaptive_timebase(params)
%GENERATE_ADAPTIVE_TIMEBASE 生成高精度自适应时间向量和信号
%   为高速流水线ADC生成自适应时间基准，包括正弦波和非交叠时钟信号
%   符合相干采样要求：输入频率 = 23/1024*100e6 Hz，30个周期
%
%   输入参数:
%       params - ADC参数结构体，包含以下字段:
%           fs - ADC采样频率 (Hz)
%           f_in - 输入正弦波频率 (Hz) [建议使用23/1024*100e6]
%           Vref - 参考电压 (V)
%           Vcm - 共模电压 (V)
%           clks_width - 采样时钟脉宽系数 (占空比)
%           clkh_delay - 保持时钟相位延迟系数
%           num_sine_cycles - 仿真正弦波周期数 (默认30)
%
%   输出参数:
%       t - 自适应时间向量
%       sine_wave - 正弦波信号
%       clks - 采样时钟信号 (CLKS)
%       clkh - 保持时钟信号 (CLKH)

%% 参数设置
fs_adc = params.fs;
f_clk = fs_adc;
f_sin = params.f_in;
A_sin = 1 * params.Vref;

% 仿真时长
if isfield(params, 'num_sine_cycles')
    num_cycles = params.num_sine_cycles;
else
    num_cycles = 30;
end
t_sim = num_cycles / f_sin;

% 时钟参数设置
clks_width = 0.48/fs_adc;
clks_delay = 0.5e-9;
clkh_width = 0.48/fs_adc;
clkh_delay = 5.5e-9;

%% 生成自适应时间向量
t_edges = [];

% 时钟边沿
num_clk_cycles = ceil(t_sim * f_clk);
for n = 0:num_clk_cycles
    % CLKS时钟边沿
    clks_start = n/f_clk + clks_delay;
    if clks_start <= t_sim
        t_edges = [t_edges, clks_start, clks_start + clks_width];
    end
    
    % CLKH时钟边沿
    clkh_start = n/f_clk + clkh_delay;
    if clkh_start <= t_sim && clkh_start + clkh_width <= t_sim
        t_edges = [t_edges, clkh_start, clkh_start + clkh_width];
    end
end

% 正弦波特征点
t_sin_features = 0 : 1/(8*f_sin) : t_sim;

% 合并并排序所有关键点
key_points = unique(sort([t_edges, t_sin_features]));
key_points = key_points(key_points <= t_sim);

% 在关键点之间插入自适应步长
t = [];
max_step_limit = 0.1e-9;              % 最大时间间隔限制: 0.1ns

for k = 1:length(key_points)-1
    interval = key_points(k+1) - key_points(k);
    
    % 自适应步长策略
    if interval > 1/(100*f_clk)        
        step = min([interval/10, 1/(20*f_sin), max_step_limit]);
    else                               
        step = min([interval/50, 1/(1000*f_clk), max_step_limit]);
    end
    
    % 步长限制
    min_step = 1/(10000*f_clk);        
    step = max(step, min_step);
    step = min(step, max_step_limit);
    
    % 生成区间内的时间点
    if step > 0
        t_interval = key_points(k) : step : key_points(k+1);
        t = [t, t_interval];
    end
end

% 添加最后一个关键点
if ~isempty(key_points)
    t = [t, key_points(end)];
end

% 去重并排序
t = unique(t);
t = t(t <= t_sim);

%% 生成正弦波信号
sine_wave = A_sin * sin(2*pi*f_sin*t);

%% 生成非交叠时钟
% CLKS (主时钟)
clks = zeros(size(t));
num_clk_periods = floor(t_sim * f_clk) + 1;

for n = 0:num_clk_periods
    rise_time = n/f_clk + clks_delay;
    fall_time = rise_time + clks_width;
    
    if rise_time >= 0 && rise_time <= t_sim
        clks((t >= rise_time) & (t <= fall_time)) = 1;
    end
end

% CLKH (辅助时钟)
clkh = zeros(size(t));

for n = 0:num_clk_periods
    rise_time = n/f_clk + clkh_delay;
    fall_time = rise_time + clkh_width;
    
    if rise_time >= 0 && rise_time <= t_sim
        clkh((t >= rise_time) & (t <= fall_time)) = 1;
    end
end

% 确保所有输出都是列向量以避免维度问题
t = t(:);
sine_wave = sine_wave(:);
clks = clks(:);
clkh = clkh(:);

end 
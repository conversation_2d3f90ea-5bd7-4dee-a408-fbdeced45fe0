function [adc_output, binary_output, stage_history, timing_info] = event_driven_pipeline_adc(Vin_p, Vin_n, params, rel_error_params, t_sampled, clks_input, clkh_input)
%EVENT_DRIVEN_PIPELINE_ADC 带时序约束的流水线ADC MATLAB模型
%   根据详细技术规范实现完整的流水线ADC模型
%
%   技术规范要求:
%   1. 输入信号: 频率=23/1024*100e6 Hz，30个周期，相干采样
%   2. 模型输出: 两种模拟输出 + 理想ADC对比基准
%   3. 时序控制: 非交叠时钟(CLKH,CLKS)，每级延迟T/2
%   4. SHA电路: 时钟下降沿锁存，上升沿保持输出
%   5. 1.5bit流水线级: 阈值[-1/4Vref, 1/4Vref]，传递函数实现
%   6. Flash ADC: 2bit完整量化，延迟4.5个周期
%   7. 数字校准: 18bit→10bit全加器累加，理想DAC转换
%
%   输入参数:
%       Vin_p, Vin_n - 差分输入信号
%       params - ADC参数结构体
%       rel_error_params - 相对误差参数矩阵
%       t_sampled - 时间向量
%       clks_input, clkh_input - 非交叠时钟信号
%
%   输出参数:
%       adc_output - 最终DAC转换的模拟信号输出(模拟输出2)
%       binary_output - 10bit有效数字输出
%       stage_history - 各级历史数据(包含模拟输出1)
%       timing_info - 时序信息和性能统计

    % 参数验证和初始化
    [Vin_p, Vin_n, t_sampled, clks_input, clkh_input] = validate_inputs(...
        Vin_p, Vin_n, t_sampled, clks_input, clkh_input);

    num_samples = length(t_sampled);
    num_stages = params.num_stages;

    fprintf('=== 带时序约束的流水线ADC MATLAB模型 ===\n');
    fprintf('技术规范: 输入频率=%.3fMHz, %d个周期, %d级流水线\n', ...
            params.f_in/1e6, params.num_cycles, num_stages);
    fprintf('时序控制: 非交叠时钟(CLKH,CLKS), 每级延迟T/2\n');
    fprintf('样本数: %d, 时间范围: [%.6f, %.6f]s\n', ...
            num_samples, min(t_sampled), max(t_sampled));

    % 验证输入频率是否符合设计要求 (23/1024*100e6)
    expected_freq = 23/1024*100e6;
    if abs(params.f_in - expected_freq) > 1e3
        warning('输入频率 %.3f MHz 偏离设计要求 %.3f MHz', ...
                params.f_in/1e6, expected_freq/1e6);
    end

    % 初始化时序控制系统
    timing_control = initialize_timing_control_system(num_stages, params);

    % 初始化事件队列系统
    event_system = initialize_event_system(t_sampled, clks_input, clkh_input, params);

    % 验证事件系统有效性
    if event_system.num_events == 0
        error('未检测到有效的时钟事件，无法运行事件驱动ADC模型');
    end

    % 初始化输出存储结构
    [stage_history, adc_output, binary_output] = initialize_output_storage(num_samples, num_stages);

    % 主事件驱动处理循环
    fprintf('开始事件驱动处理...\n');

    % 调用改进的事件处理核心，实现完整的技术规范
    [adc_output, binary_output, stage_history, timing_info] = ...
        enhanced_event_processing_core(Vin_p, Vin_n, t_sampled, event_system, ...
                                     timing_control, rel_error_params, params);

    fprintf('带时序约束的流水线ADC处理完成\n');
    fprintf('有效输出样本: %d/%d (%.1f%%)\n', ...
            sum(abs(adc_output) > 0.001), num_samples, ...
            100*sum(abs(adc_output) > 0.001)/num_samples);
end

function delay_buffers = initialize_delay_buffers(num_stages, params)
%INITIALIZE_DELAY_BUFFERS 初始化延迟缓冲系统
%   为每一级创建真正的延迟缓冲寄存器

    delay_buffers = struct();
    
    % SHA延迟缓冲 (1级延迟)
    delay_buffers.sha = struct(...
        'data', zeros(2, 1), ...        % 数据缓冲
        'valid', false(2, 1), ...       % 有效标志
        'timestamp', zeros(2, 1));      % 时间戳
    
    % 流水线级延迟缓冲 (每级2个时钟延迟)
    for stage = 1:num_stages
        delay_buffers.(['stage_', num2str(stage)]) = struct(...
            'analog_data', zeros(3, 1), ...     % 模拟数据缓冲 (3级深度)
            'digital_data', zeros(3, 2), ...    % 数字数据缓冲 (3级深度, 2位)
            'valid_analog', false(3, 1), ...    % 模拟数据有效标志
            'valid_digital', false(3, 1), ...   % 数字数据有效标志
            'timestamp', zeros(3, 1));          % 时间戳
    end
    
    % Flash ADC延迟缓冲 (1级延迟)
    delay_buffers.flash = struct(...
        'data', zeros(2, 2), ...        % 数字数据缓冲 (2位)
        'valid', false(2, 1), ...       % 有效标志
        'timestamp', zeros(2, 1));      % 时间戳
    
    fprintf('延迟缓冲系统初始化完成\n');
end

function event_system = initialize_event_system(t_sampled, clks, clkh, params)
%INITIALIZE_EVENT_SYSTEM 初始化事件驱动系统
%   检测所有时钟边沿并创建事件队列

    event_system = struct();
    
    % 检测CLKS时钟边沿
    clks_rising = find(diff([0; clks]) > 0.5);
    clks_falling = find(diff([clks; 0]) < -0.5);
    
    % 检测CLKH时钟边沿
    clkh_rising = find(diff([0; clkh]) > 0.5);
    clkh_falling = find(diff([clkh; 0]) < -0.5);
    
    % 验证时钟信号有效性
    if isempty(clks_rising) && isempty(clks_falling) && isempty(clkh_rising) && isempty(clkh_falling)
        error('未检测到有效的时钟边沿，请检查时钟信号是否正确生成');
    end
    
    % 创建事件列表
    events = [];
    
    % CLKS事件
    for i = 1:length(clks_rising)
        events = [events; struct('time', t_sampled(clks_rising(i)), ...
                                'index', clks_rising(i), ...
                                'type', 'CLKS_RISING', ...
                                'clock', 'CLKS')];
    end
    
    for i = 1:length(clks_falling)
        events = [events; struct('time', t_sampled(clks_falling(i)), ...
                                'index', clks_falling(i), ...
                                'type', 'CLKS_FALLING', ...
                                'clock', 'CLKS')];
    end
    
    % CLKH事件
    for i = 1:length(clkh_rising)
        events = [events; struct('time', t_sampled(clkh_rising(i)), ...
                                'index', clkh_rising(i), ...
                                'type', 'CLKH_RISING', ...
                                'clock', 'CLKH')];
    end
    
    for i = 1:length(clkh_falling)
        events = [events; struct('time', t_sampled(clkh_falling(i)), ...
                                'index', clkh_falling(i), ...
                                'type', 'CLKH_FALLING', ...
                                'clock', 'CLKH')];
    end
    
    % 按时间排序事件
    if ~isempty(events)
        [~, sort_idx] = sort([events.time]);
        event_system.events = events(sort_idx);
    else
        event_system.events = [];
    end
    event_system.num_events = length(events);
    
    fprintf('事件系统初始化完成: %d个事件\n', event_system.num_events);
    fprintf('  CLKS上升沿: %d个, CLKS下降沿: %d个\n', length(clks_rising), length(clks_falling));
    fprintf('  CLKH上升沿: %d个, CLKH下降沿: %d个\n', length(clkh_rising), length(clkh_falling));
end

function stage_history = initialize_stage_history(num_samples, num_stages)
%INITIALIZE_STAGE_HISTORY 初始化各级历史数据存储

    stage_history = struct();
    stage_history.sha = zeros(num_samples, 1);
    
    for stage = 1:num_stages
        stage_history.(['stage_', num2str(stage), '_analog']) = zeros(num_samples, 1);
        stage_history.(['stage_', num2str(stage), '_digital']) = zeros(num_samples, 2);
    end
    
    stage_history.flash = zeros(num_samples, 2);
    stage_history.valid_mask = false(num_samples, 1);
end

function [Vin_p, Vin_n, t_sampled, clks_input, clkh_input] = validate_inputs(...
    Vin_p, Vin_n, t_sampled, clks_input, clkh_input)
%VALIDATE_INPUTS 验证和格式化输入参数

    Vin_p = Vin_p(:);
    Vin_n = Vin_n(:);
    t_sampled = t_sampled(:);
    clks_input = clks_input(:);
    clkh_input = clkh_input(:);
    
    if length(Vin_p) ~= length(t_sampled) || length(clks_input) ~= length(t_sampled)
        error('所有输入向量长度必须一致');
    end
    
    % 验证输入信号的有效性
    if any(isnan(Vin_p)) || any(isnan(Vin_n))
        error('输入信号包含NaN值，无法进行ADC处理');
    end
    
    if any(isinf(Vin_p)) || any(isinf(Vin_n))
        error('输入信号包含无穷大值，无法进行ADC处理');
    end
    
    % 验证时钟信号的有效性
    if all(clks_input == clks_input(1)) || all(clkh_input == clkh_input(1))
        error('时钟信号无变化，无法生成时钟事件');
    end
end

function timing_control = initialize_timing_control_system(num_stages, params)
%INITIALIZE_TIMING_CONTROL_SYSTEM 初始化时序控制系统
%   实现完整的时钟分配方案和延迟管理

    timing_control = struct();

    % 时钟分配方案 (根据技术规范)
    % SHA:      采样态(CLKH) → 保持态(CLKS)
    % STAGE1:   采样态(CLKS) → 保持态(CLKH)
    % STAGE2:   采样态(CLKH) → 保持态(CLKS)
    % ...
    % STAGE8:   采样态(CLKH) → 保持态(CLKS)
    % Flash ADC: 采样态(CLKS) → 保持态(CLKH)

    timing_control.clock_assignment = struct();
    timing_control.clock_assignment.sha = struct('sample_clk', 'CLKH', 'hold_clk', 'CLKS');

    for stage = 1:num_stages
        if mod(stage, 2) == 1  % 奇数级 (STAGE1,3,5,7)
            timing_control.clock_assignment.(['stage_', num2str(stage)]) = ...
                struct('sample_clk', 'CLKS', 'hold_clk', 'CLKH');
        else  % 偶数级 (STAGE2,4,6,8)
            timing_control.clock_assignment.(['stage_', num2str(stage)]) = ...
                struct('sample_clk', 'CLKH', 'hold_clk', 'CLKS');
        end
    end

    timing_control.clock_assignment.flash = struct('sample_clk', 'CLKS', 'hold_clk', 'CLKH');

    % 延迟管理配置 (每级相对前级延迟T/2)
    timing_control.delay_config = struct();
    timing_control.delay_config.half_period = 1 / (2 * params.fs);  % T/2

    % 数字校准延迟同步配置
    % STAGE1输出延迟4个周期, STAGE2延迟3.5个周期, ..., STAGE8延迟0.5个周期
    timing_control.digital_correction_delays = zeros(num_stages + 1, 1);  % +1 for Flash
    for stage = 1:num_stages
        timing_control.digital_correction_delays(stage) = (num_stages - stage + 1) * 0.5;
    end
    timing_control.digital_correction_delays(num_stages + 1) = 0.5;  % Flash ADC延迟0.5个周期

    fprintf('时序控制系统初始化完成\n');
    fprintf('  时钟分配: SHA(CLKH→CLKS), 奇数级(CLKS→CLKH), 偶数级(CLKH→CLKS)\n');
    fprintf('  延迟管理: T/2=%.3fns, 数字校准延迟范围[0.5, 4.0]周期\n', ...
            timing_control.delay_config.half_period*1e9);
end

function [stage_history, adc_output, binary_output] = initialize_output_storage(num_samples, num_stages)
%INITIALIZE_OUTPUT_STORAGE 初始化输出存储结构
%   为两种模拟输出和数字输出分配存储空间

    % 初始化各级历史数据存储 (模拟输出1: SHA + 8级流水线模拟输出)
    stage_history = struct();
    stage_history.sha = zeros(num_samples, 1);  % SHA模拟输出

    for stage = 1:num_stages
        stage_history.(['stage_', num2str(stage), '_analog']) = zeros(num_samples, 1);
        stage_history.(['stage_', num2str(stage), '_digital']) = zeros(num_samples, 2);
    end

    stage_history.flash = zeros(num_samples, 2);  % Flash ADC数字输出
    stage_history.valid_mask = false(num_samples, 1);

    % 初始化最终输出 (模拟输出2: 18bit→10bit→DAC转换)
    adc_output = zeros(num_samples, 1);      % 最终DAC转换的模拟信号
    binary_output = zeros(num_samples, 10);  % 10bit有效数字输出

    fprintf('输出存储结构初始化完成\n');
    fprintf('  模拟输出1: SHA + %d级流水线模拟输出\n', num_stages);
    fprintf('  模拟输出2: 18bit→10bit→DAC转换模拟输出\n');
end
function [corrected_analog_output, corrected_binary_output, correction_metrics] = ...
    digital_correction_v4(digital_data_struct, analog_data_struct, t, params)
%DIGITAL_CORRECTION_V4 数字校正模块 (v4版本) - 简化RSD校正
%   VERSION HISTORY:
%   v1-v3: 基于stage_history的复杂标签对齐机制
%   v4: 基于数据矩阵的时延对齐和RSD校正重构版本
%   v4.1: 简化版本 - 延迟对齐迁移到事件驱动核心
%
%   v4.1版本核心特性:
%   1. 处理预对齐的数字数据结构体 (18位独立存储)
%   2. 延迟对齐已在事件驱动核心完成，无需重复处理
%   3. 直接执行RSD校正算法：18bit → 全加器逻辑 → 10bit
%   4. 权重赋值与归一化操作
%   5. 简化的数据流，提高处理效率
%
%   输入参数:
%       digital_data_struct - 数字数据结构体 (18位独立存储)
%       analog_data_struct - 模拟数据结构体 (9条线路独立存储)
%   
%     t - 时间向量
%       params - ADC参数
%
%   输出参数:
%       corrected_analog_output - 校正后的模拟输出 (N×1)
%       corrected_binary_output - 校正后的10bit数字输出 (N×10)
%       correction_metrics - 校正性能统计

    fprintf('=== 数字校正模块 v4.0 (完整时延对齐和RSD校正) ===\n');
    
    %% 初始化校正系统
    % 从数字数据结构体获取样本数量
    num_samples = length(digital_data_struct.signals(1).values);
    fs = params.fs;
    
    % 初始化校正统计
    correction_metrics = struct();
    correction_metrics.total_samples = num_samples;
    correction_metrics.fs = fs;
    correction_metrics.alignment_steps = [];
    correction_metrics.rsd_corrections = 0;
    correction_metrics.valid_outputs = 0;
    correction_metrics.processing_efficiency = 0;

    %% 直接执行RSD校正算法（延迟对齐已在事件驱动核心完成）
    fprintf('\n=== RSD校正算法（基于预对齐数据） ===\n');
    [corrected_analog_output, corrected_binary_output] = ...
        perform_rsd_correction_direct(digital_data_struct, params, correction_metrics);
    
    %% 第三步：生成校正统计
    correction_metrics.valid_outputs = sum(abs(corrected_analog_output) > 0.001);
    correction_metrics.processing_efficiency = correction_metrics.valid_outputs / num_samples;
    correction_metrics.rsd_corrections = correction_metrics.valid_outputs;
    
    fprintf('\n=== 数字校正v4完成 ===\n');
    fprintf('有效校正输出: %d/%d (%.1f%%)\n', ...
            correction_metrics.valid_outputs, num_samples, ...
            correction_metrics.processing_efficiency * 100);
end

function [corrected_analog_output, corrected_binary_output] = ...
    perform_rsd_correction_direct(digital_data_struct, params, correction_metrics)
%PERFORM_RSD_CORRECTION_DIRECT 直接执行RSD校正算法（基于预对齐数据）
%   输入的数字数据结构体已经在事件驱动核心中完成了延迟对齐

    fprintf('执行RSD校正算法 (18bit → 10bit，基于预对齐数据)...\n');

    % 获取数据维度
    num_samples = length(digital_data_struct.signals(1).values);

    % 初始化输出
    corrected_analog_output = zeros(num_samples, 1);
    corrected_binary_output = zeros(num_samples, 10);

    % RSD校正参数
    Vref = params.Vref;

    % 权重设置（参考digital_correction_v3的实现）
    stage_weights = [256, 128, 64, 32, 16, 8, 4, 2];  % 8级流水线权重
    flash_weights = [2, 1];  % Flash ADC权重

    fprintf('RSD校正参数:\n');
    fprintf('  参考电压: %.3f V\n', Vref);
    fprintf('  流水线权重: [%s]\n', sprintf('%d ', stage_weights));
    fprintf('  Flash权重: [%s]\n', sprintf('%d ', flash_weights));

    %% 逐样本执行RSD校正
    valid_count = 0;

    for sample_idx = 1:num_samples
        % 提取当前样本的18bit数字码（直接从结构体读取）
        sample_18bit = zeros(18, 1);
        for bit_idx = 1:18
            sample_18bit(bit_idx) = digital_data_struct.signals(bit_idx).values(sample_idx);
        end

        % 检查样本是否有效（至少有一些非零位）
        if any(sample_18bit ~= 0)
            % 执行全加器累加算法
            [binary_10bit, rsd_success] = execute_full_adder_rsd(sample_18bit, stage_weights, flash_weights);

            if rsd_success
                % 执行权重赋值和归一化
                [analog_value, dac_success] = execute_dac_conversion(binary_10bit, Vref);

                if dac_success
                    corrected_binary_output(sample_idx, :) = binary_10bit;
                    corrected_analog_output(sample_idx) = analog_value;
                    valid_count = valid_count + 1;
                end
            end
        end

        % 进度显示（每10000个样本）
        if mod(sample_idx, 10000) == 0
            fprintf('RSD校正进度: %d/%d样本 (%.1f%%)\n', ...
                    sample_idx, num_samples, 100*sample_idx/num_samples);
        end
    end

    fprintf('RSD校正完成: 成功校正%d/%d样本 (%.1f%%)\n', ...
            valid_count, num_samples, 100*valid_count/num_samples);
end



function [binary_10bit, success] = execute_full_adder_rsd(sample_18bit, stage_weights, flash_weights)
%EXECUTE_FULL_ADDER_RSD 执行全加器RSD累加算法

    success = false;
    binary_10bit = zeros(1, 10);
    
    try
        % 第一步：解析18bit数字码为流水线级码和Flash码
        pipeline_codes = zeros(8, 2);  % 8级×2bit
        flash_code = zeros(1, 2);      % Flash×2bit
        
        % 提取流水线级数字码
        for stage = 1:8
            bit_start = (stage - 1) * 2 + 1;
            pipeline_codes(stage, :) = sample_18bit(bit_start:bit_start+1);
        end
        
        % 提取Flash ADC数字码
        flash_code = sample_18bit(17:18);
        
        % 第二步：转换为RSD表示
        rsd_values = zeros(8, 1);
        for stage = 1:8
            bits = pipeline_codes(stage, :);
            if isequal(bits, [1, 1])
                rsd_values(stage) = 1;   % +1
            elseif isequal(bits, [1, 0])
                rsd_values(stage) = 0;   % 0
            elseif isequal(bits, [0, 0])
                rsd_values(stage) = -1;  % -1
            else
                rsd_values(stage) = 0;   % 默认为0
            end
        end
        
        % Flash ADC转换
        flash_value = flash_code(1) * 2 + flash_code(2);  % 二进制转十进制
        
        % 第三步：执行全加器累加
        digital_sum = 0;
        
        % 流水线级加权累加
        for stage = 1:8
            digital_sum = digital_sum + rsd_values(stage) * stage_weights(stage);
        end
        
        % Flash ADC贡献
        digital_sum = digital_sum + flash_value;
        
        % 第四步：转换为10bit二进制
        % 偏移处理：+512使结果为正数
        digital_sum_offset = digital_sum + 512;
        
        % 限制在10bit范围内 [0, 1023]
        digital_sum_limited = max(0, min(1023, round(digital_sum_offset)));
        
        % 转换为二进制表示
        binary_10bit = de2bi(digital_sum_limited, 10, 'left-msb');
        
        success = true;
        
    catch ME
        success = false;
        binary_10bit = zeros(1, 10);
    end
end

function [analog_value, success] = execute_dac_conversion(binary_10bit, Vref)
%EXECUTE_DAC_CONVERSION 执行DAC转换（权重赋值和归一化）

    success = false;
    analog_value = 0;
    
    try
        % DAC权重：[512, 256, 128, 64, 32, 16, 8, 4, 2, 1]
        dac_weights = 2.^(9:-1:0);
        
        % 权重累加
        digital_sum = sum(binary_10bit .* dac_weights);
        
        % 偏移处理：减去512使输出以0为中心
        digital_sum_centered = digital_sum - 512;
        
        % 归一化：除以512并乘以参考电压
        analog_value = (digital_sum_centered / 512) * Vref;
        
        % 饱和保护
        analog_value = max(-Vref, min(Vref, analog_value));
        
        success = true;
        
    catch ME
        success = false;
        analog_value = 0;
    end
end 
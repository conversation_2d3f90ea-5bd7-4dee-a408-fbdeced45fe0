function [corrected_analog_output, corrected_binary_output, correction_metrics] = ...
    digital_correction_v4(digital_data_matrix, analog_data_matrix, t, params)
%DIGITAL_CORRECTION_V4 数字校正模块 (v4版本) - 完整时延对齐和RSD校正
%   VERSION HISTORY:
%   v1-v3: 基于stage_history的复杂标签对齐机制
%   v4: 基于数据矩阵的时延对齐和RSD校正重构版本
%
%   v4版本核心特性:
%   1. 处理完整数字码数组矩阵 (18×N)
%   2. 通过列数据整体移动实现时延对齐
%   3. 精确的时延参数：
%      - STAGE1: 向下移动4个采样周期 (4×10ns×1000步)
%      - STAGE2: 向下移动3.5个周期
%      - STAGE3: 向下移动3个周期  
%      - STAGE4: 向下移动2.5个周期
%      - STAGE5: 向下移动2个周期
%      - STAGE6: 向下移动1.5个周期
%      - STAGE7: 向下移动1个周期
%      - STAGE8和Flash ADC: 向下移动0.5个周期
%   4. 利用数组末端冗余量避免数据丢失
%   5. 执行完整RSD校正算法：18bit → 全加器逻辑 → 10bit
%   6. 权重赋值与归一化操作
%
%   输入参数:
%       digital_data_matrix - 数字数据矩阵 (18×N)
%       analog_data_matrix - 模拟数据矩阵 (9×N) 
%       t - 时间向量
%       params - ADC参数
%
%   输出参数:
%       corrected_analog_output - 校正后的模拟输出 (N×1)
%       corrected_binary_output - 校正后的10bit数字输出 (N×10)
%       correction_metrics - 校正性能统计

    fprintf('=== 数字校正模块 v4.0 (完整时延对齐和RSD校正) ===\n');
    
    %% 初始化校正系统
    num_samples = size(digital_data_matrix, 2);
    fs = params.fs;
    
    % 初始化输出
    corrected_analog_output = zeros(num_samples, 1);
    corrected_binary_output = zeros(num_samples, 10);
    
    % 初始化校正统计
    correction_metrics = struct();
    correction_metrics.total_samples = num_samples;
    correction_metrics.fs = fs;
    correction_metrics.alignment_steps = [];
    correction_metrics.rsd_corrections = 0;
    correction_metrics.valid_outputs = 0;
    correction_metrics.processing_efficiency = 0;
    
    fprintf('输入数据: 数字矩阵%s, 模拟矩阵%s, 时间点%d\n', ...
            mat2str(size(digital_data_matrix)), mat2str(size(analog_data_matrix)), num_samples);
    
    %% 第一步：时延对齐处理
    fprintf('\n=== 第一步：时延对齐处理 ===\n');
    aligned_digital_matrix = perform_delay_alignment(digital_data_matrix, fs, correction_metrics);
    
    %% 第二步：RSD校正算法
    fprintf('\n=== 第二步：RSD校正算法 ===\n');
    [corrected_analog_output, corrected_binary_output] = ...
        perform_rsd_correction(aligned_digital_matrix, params, correction_metrics);
    
    %% 第三步：生成校正统计
    correction_metrics.valid_outputs = sum(abs(corrected_analog_output) > 0.001);
    correction_metrics.processing_efficiency = correction_metrics.valid_outputs / num_samples;
    correction_metrics.rsd_corrections = correction_metrics.valid_outputs;
    
    fprintf('\n=== 数字校正v4完成 ===\n');
    fprintf('有效校正输出: %d/%d (%.1f%%)\n', ...
            correction_metrics.valid_outputs, num_samples, ...
            correction_metrics.processing_efficiency * 100);
end

function aligned_digital_matrix = perform_delay_alignment(digital_data_matrix, fs, correction_metrics)
%PERFORM_DELAY_ALIGNMENT 执行时延对齐处理

    fprintf('执行时延对齐处理...\n');
    
    % 获取矩阵维度
    [num_bits, num_samples] = size(digital_data_matrix);
    
    % 初始化对齐后的矩阵
    aligned_digital_matrix = zeros(size(digital_data_matrix));
    
    % 时延参数设置（以采样周期为单位）
    delay_cycles = [
        4.0;  % STAGE1: 4个周期
        3.5;  % STAGE2: 3.5个周期  
        3.0;  % STAGE3: 3个周期
        2.5;  % STAGE4: 2.5个周期
        2.0;  % STAGE5: 2个周期
        1.5;  % STAGE6: 1.5个周期
        1.0;  % STAGE7: 1个周期
        0.5;  % STAGE8: 0.5个周期
        0.5   % Flash ADC: 0.5个周期
    ];
    
    % 转换为时间步长数（1个采样周期 = 1000个时间步）
    steps_per_cycle = 1000;  % 10ps × 1000 = 10ns = 1个采样周期
    
    fprintf('时延对齐参数:\n');
    fprintf('  采样频率: %.1f MHz\n', fs/1e6);
    fprintf('  时间步长: 10 ps\n');
    fprintf('  每周期步数: %d步\n', steps_per_cycle);
    
    % 处理每个级别的时延对齐
    bit_idx = 1;
    for stage = 1:8
        % 每级2bit
        stage_bits = bit_idx:bit_idx+1;
        delay_steps = round(delay_cycles(stage) * steps_per_cycle); % 向下移动的步数
        
        fprintf('  STAGE%d: 延迟%.1f周期 = %d步\n', stage, delay_cycles(stage), delay_steps);
        
        % 执行向下移动（列数据整体移动）
        if delay_steps > 0 && delay_steps < num_samples
            % 向下移动：后面的数据移动到前面
            aligned_digital_matrix(stage_bits, 1:num_samples-delay_steps) = ...
                digital_data_matrix(stage_bits, delay_steps+1:end);
            % 末端填零（利用预留的冗余空间）
            aligned_digital_matrix(stage_bits, num_samples-delay_steps+1:end) = 0;
        else
            % 延迟过大或为0，直接复制
            aligned_digital_matrix(stage_bits, :) = digital_data_matrix(stage_bits, :);
        end
        
        bit_idx = bit_idx + 2;
        correction_metrics.alignment_steps(stage) = delay_steps;
    end
    
    % 处理Flash ADC (最后2bit)
    flash_delay_steps = round(delay_cycles(9) * steps_per_cycle);
    fprintf('  Flash ADC: 延迟%.1f周期 = %d步\n', delay_cycles(9), flash_delay_steps);
    
    flash_bits = 17:18;
    if flash_delay_steps > 0 && flash_delay_steps < num_samples
        aligned_digital_matrix(flash_bits, 1:num_samples-flash_delay_steps) = ...
            digital_data_matrix(flash_bits, flash_delay_steps+1:end);
        aligned_digital_matrix(flash_bits, num_samples-flash_delay_steps+1:end) = 0;
    else
        aligned_digital_matrix(flash_bits, :) = digital_data_matrix(flash_bits, :);
    end
    
    correction_metrics.alignment_steps(9) = flash_delay_steps;
    
    % 统计对齐效果
    original_active_samples = sum(any(digital_data_matrix ~= 0, 1));
    aligned_active_samples = sum(any(aligned_digital_matrix ~= 0, 1));
    
    fprintf('时延对齐完成:\n');
    fprintf('  原始活跃样本: %d\n', original_active_samples);
    fprintf('  对齐后活跃样本: %d\n', aligned_active_samples);
    fprintf('  保持率: %.1f%%\n', 100 * aligned_active_samples / max(1, original_active_samples));
end

function [corrected_analog_output, corrected_binary_output] = ...
    perform_rsd_correction(aligned_digital_matrix, params, correction_metrics)
%PERFORM_RSD_CORRECTION 执行RSD校正算法

    fprintf('执行RSD校正算法 (18bit → 10bit)...\n');
    
    [num_bits, num_samples] = size(aligned_digital_matrix);
    
    % 初始化输出
    corrected_analog_output = zeros(num_samples, 1);
    corrected_binary_output = zeros(num_samples, 10);
    
    % RSD校正参数
    Vref = params.Vref;
    
    % 权重设置（参考digital_correction_v3的实现）
    stage_weights = [256, 128, 64, 32, 16, 8, 4, 2];  % 8级流水线权重
    flash_weights = [2, 1];  % Flash ADC权重
    
    fprintf('RSD校正参数:\n');
    fprintf('  参考电压: %.3f V\n', Vref);
    fprintf('  流水线权重: [%s]\n', sprintf('%d ', stage_weights));
    fprintf('  Flash权重: [%s]\n', sprintf('%d ', flash_weights));
    
    %% 逐样本执行RSD校正
    valid_count = 0;
    
    for sample_idx = 1:num_samples
        % 提取当前样本的18bit数字码
        sample_18bit = aligned_digital_matrix(:, sample_idx);
        
        % 检查样本是否有效（至少有一些非零位）
        if any(sample_18bit ~= 0)
            % 执行全加器累加算法
            [binary_10bit, rsd_success] = execute_full_adder_rsd(sample_18bit, stage_weights, flash_weights);
            
            if rsd_success
                % 执行权重赋值和归一化
                [analog_value, dac_success] = execute_dac_conversion(binary_10bit, Vref);
                
                if dac_success
                    corrected_binary_output(sample_idx, :) = binary_10bit;
                    corrected_analog_output(sample_idx) = analog_value;
                    valid_count = valid_count + 1;
                end
            end
        end
        
        % 进度显示（每10000个样本）
        if mod(sample_idx, 10000) == 0
            fprintf('RSD校正进度: %d/%d样本 (%.1f%%)\n', ...
                    sample_idx, num_samples, 100*sample_idx/num_samples);
        end
    end
    
    fprintf('RSD校正完成: 成功校正%d/%d样本 (%.1f%%)\n', ...
            valid_count, num_samples, 100*valid_count/num_samples);
end

function [binary_10bit, success] = execute_full_adder_rsd(sample_18bit, stage_weights, flash_weights)
%EXECUTE_FULL_ADDER_RSD 执行全加器RSD累加算法

    success = false;
    binary_10bit = zeros(1, 10);
    
    try
        % 第一步：解析18bit数字码为流水线级码和Flash码
        pipeline_codes = zeros(8, 2);  % 8级×2bit
        flash_code = zeros(1, 2);      % Flash×2bit
        
        % 提取流水线级数字码
        for stage = 1:8
            bit_start = (stage - 1) * 2 + 1;
            pipeline_codes(stage, :) = sample_18bit(bit_start:bit_start+1);
        end
        
        % 提取Flash ADC数字码
        flash_code = sample_18bit(17:18);
        
        % 第二步：转换为RSD表示
        rsd_values = zeros(8, 1);
        for stage = 1:8
            bits = pipeline_codes(stage, :);
            if isequal(bits, [1, 1])
                rsd_values(stage) = 1;   % +1
            elseif isequal(bits, [1, 0])
                rsd_values(stage) = 0;   % 0
            elseif isequal(bits, [0, 0])
                rsd_values(stage) = -1;  % -1
            else
                rsd_values(stage) = 0;   % 默认为0
            end
        end
        
        % Flash ADC转换
        flash_value = flash_code(1) * 2 + flash_code(2);  % 二进制转十进制
        
        % 第三步：执行全加器累加
        digital_sum = 0;
        
        % 流水线级加权累加
        for stage = 1:8
            digital_sum = digital_sum + rsd_values(stage) * stage_weights(stage);
        end
        
        % Flash ADC贡献
        digital_sum = digital_sum + flash_value;
        
        % 第四步：转换为10bit二进制
        % 偏移处理：+512使结果为正数
        digital_sum_offset = digital_sum + 512;
        
        % 限制在10bit范围内 [0, 1023]
        digital_sum_limited = max(0, min(1023, round(digital_sum_offset)));
        
        % 转换为二进制表示
        binary_10bit = de2bi(digital_sum_limited, 10, 'left-msb');
        
        success = true;
        
    catch ME
        success = false;
        binary_10bit = zeros(1, 10);
    end
end

function [analog_value, success] = execute_dac_conversion(binary_10bit, Vref)
%EXECUTE_DAC_CONVERSION 执行DAC转换（权重赋值和归一化）

    success = false;
    analog_value = 0;
    
    try
        % DAC权重：[512, 256, 128, 64, 32, 16, 8, 4, 2, 1]
        dac_weights = 2.^(9:-1:0);
        
        % 权重累加
        digital_sum = sum(binary_10bit .* dac_weights);
        
        % 偏移处理：减去512使输出以0为中心
        digital_sum_centered = digital_sum - 512;
        
        % 归一化：除以512并乘以参考电压
        analog_value = (digital_sum_centered / 512) * Vref;
        
        % 饱和保护
        analog_value = max(-Vref, min(Vref, analog_value));
        
        success = true;
        
    catch ME
        success = false;
        analog_value = 0;
    end
end 
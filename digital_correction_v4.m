function [corrected_analog_output, corrected_binary_output, correction_metrics] = ...
    digital_correction_v4(digital_data_struct, params)
%DIGITAL_CORRECTION_V4 数字校正模块 (v4版本) - 标准全加器链式校正
%   VERSION HISTORY:
%   v1-v3: 基于stage_history的复杂标签对齐机制和RSD校正
%   v4: 基于数据矩阵的时延对齐和标准全加器链式校正重构版本
%   v4.1: 简化版本 - 延迟对齐迁移到事件驱动核心
%
%   v4.1版本核心特性:
%   1. 处理预对齐的数字数据结构体 (18位独立存储)
%   2. 延迟对齐已在事件驱动核心完成，无需重复处理
%   3. 直接执行标准全加器链式校正：18bit → 全加器链 → 10bit
%   4. 理想DAC转换：权重赋值、偏移处理、归一化操作
%   5. 简化的数据流，提高处理效率和算法准确性
%
%   输入参数:
%       digital_data_struct - 数字数据结构体 (18位独立存储)
%       params - ADC参数
%
%   输出参数:
%       corrected_analog_output - 校正后的模拟输出 (N×1)
%       corrected_binary_output - 校正后的10bit数字输出 (N×10)
%       correction_metrics - 校正性能统计

    fprintf('=== 数字校正模块 v4.0 (完整时延对齐和RSD校正) ===\n');
    
    %% 初始化校正系统
    % 从数字数据结构体获取样本数量
    num_samples = length(digital_data_struct.signals(1).values);
    fs = params.fs;
    
    % 初始化校正统计
    correction_metrics = struct();
    correction_metrics.total_samples = num_samples;
    correction_metrics.fs = fs;
    correction_metrics.alignment_steps = [];
    correction_metrics.chain_corrections = 0;
    correction_metrics.valid_outputs = 0;
    correction_metrics.processing_efficiency = 0;

    %% 直接执行标准全加器链式校正算法（延迟对齐已在事件驱动核心完成）
    fprintf('\n=== 标准全加器链式校正算法（基于预对齐数据） ===\n');
    [corrected_analog_output, corrected_binary_output] = ...
        perform_rsd_correction(digital_data_struct, params, correction_metrics);
    
    %% 第三步：生成校正统计
    correction_metrics.valid_outputs = sum(abs(corrected_analog_output) > 0.001);
    correction_metrics.processing_efficiency = correction_metrics.valid_outputs / num_samples;
    correction_metrics.chain_corrections = correction_metrics.valid_outputs;
    
    fprintf('\n=== 数字校正v4完成 ===\n');
    fprintf('有效校正输出: %d/%d (%.1f%%)\n', ...
            correction_metrics.valid_outputs, num_samples, ...
            correction_metrics.processing_efficiency * 100);
end

function [corrected_analog_output, corrected_binary_output] = ...
    perform_rsd_correction(digital_data_struct, params)
%PERFORM_STANDARD_CORRECTION_DIRECT 直接执行标准全加器链式校正算法（基于预对齐数据）
%   输入的数字数据结构体已经在事件驱动核心中完成了延迟对齐
%   实现完整的18bit→全加器链→10bit→理想DAC流程

    fprintf('执行标准全加器链式校正算法 (18bit → 10bit，基于预对齐数据)...\n');

    % 获取数据维度
    num_samples = length(digital_data_struct.signals(1).values);

    % 初始化输出
    corrected_analog_output = zeros(num_samples, 1);
    corrected_binary_output = zeros(num_samples, 10);

    % 数字校正参数
    Vref = params.Vref;

    %% 逐样本执行标准全加器链式校正
    valid_count = 0;

    for sample_idx = 1:num_samples
        % 提取当前样本的18bit数字码（直接从结构体读取）
        sample_18bit = zeros(18, 1);
        for bit_idx = 1:18
            sample_18bit(bit_idx) = digital_data_struct.signals(bit_idx).values(sample_idx);
        end

        % 处理所有样本（包括全0样本，因为全0也是有效的数字输入）
        if true  % 处理所有样本
            % 执行标准全加器链式累加算法
            [binary_10bit, chain_success] = execute_standard_full_adder_chain(sample_18bit);

            if chain_success
                % 执行理想DAC转换（权重赋值、偏移和归一化）
                [analog_value, dac_success] = execute_ideal_dac_conversion(binary_10bit, Vref);

                if dac_success
                    corrected_binary_output(sample_idx, :) = binary_10bit;
                    corrected_analog_output(sample_idx) = analog_value;
                    valid_count = valid_count + 1;
                end
            end
        end

        % 进度显示（每10000个样本）
        if mod(sample_idx, 10000) == 0
            fprintf('全加器链式校正进度: %d/%d样本 (%.1f%%)\n', ...
                    sample_idx, num_samples, 100*sample_idx/num_samples);
        end
    end

    fprintf('全加器链式校正完成: 成功校正%d/%d样本 (%.1f%%)\n', ...
            valid_count, num_samples, 100*valid_count/num_samples);
end



function [binary_10bit, success] = execute_standard_full_adder_chain(sample_18bit)
%EXECUTE_STANDARD_FULL_ADDER_CHAIN 执行标准全加器链式累加算法
%   实现18bit独立数字位到10bit二进制码的标准转换流程
%   
%   算法流程：
%   输入：D0(MSB,STAGE1) → D17(LSB,Flash ADC)
%   输出：B0(MSB) → B9(LSB)
%   
%   全加器链：
%   B9 = D17                           (直接赋值)
%   D16 + D15 + 0    = B8 + C8        (全加器1)
%   D14 + D13 + C8   = B7 + C7        (全加器2)
%   D12 + D11 + C7   = B6 + C6        (全加器3)
%   D10 + D9  + C6   = B5 + C5        (全加器4)
%   D8  + D7  + C5   = B4 + C4        (全加器5)
%   D6  + D5  + C4   = B3 + C3        (全加器6)
%   D4  + D3  + C3   = B2 + C2        (全加器7)
%   D2  + D1  + C2   = B1 + C1        (全加器8)
%   D0  + 0   + C1   = B0 + C0(丢弃)   (全加器9)
    
    try
        % 检查输入数据有效性
        if length(sample_18bit) ~= 18
            error('输入数据必须为18位');
        end
        
        % 确保输入为二进制位（0或1）
        D = round(sample_18bit);  % D(1)=D0, D(2)=D1, ..., D(18)=D17
        D = max(0, min(1, D));    % 限制在[0,1]范围内
        
        % 初始化输出位和进位
        B = zeros(1, 10);  % B(1)=B0, B(2)=B1, ..., B(10)=B9
        C = 0;             % 初始进位为0
        
        % 第一步：B9 = D17 (直接赋值，最低位)
        B(10) = D(18);     % B9 = D17
        
        % 第二步：全加器链式累加 (从低位到高位)
        % 全加器1: D16 + D15 + 0 = B8 + C8
        [B(9), C] = standard_full_adder(D(17), D(16), C);
        
        % 全加器2: D14 + D13 + C8 = B7 + C7
        [B(8), C] = standard_full_adder(D(15), D(14), C);
        
        % 全加器3: D12 + D11 + C7 = B6 + C6
        [B(7), C] = standard_full_adder(D(13), D(12), C);
        
        % 全加器4: D10 + D9 + C6 = B5 + C5
        [B(6), C] = standard_full_adder(D(11), D(10), C);
        
        % 全加器5: D8 + D7 + C5 = B4 + C4
        [B(5), C] = standard_full_adder(D(9), D(8), C);
        
        % 全加器6: D6 + D5 + C4 = B3 + C3
        [B(4), C] = standard_full_adder(D(7), D(6), C);
        
        % 全加器7: D4 + D3 + C3 = B2 + C2
        [B(3), C] = standard_full_adder(D(5), D(4), C);
        
        % 全加器8: D2 + D1 + C2 = B1 + C1
        [B(2), C] = standard_full_adder(D(3), D(2), C);
        
        % 全加器9: D0 + 0 + C1 = B0 + C0(丢弃)
        [B(1), ~] = standard_full_adder(D(1), 0, C);
        
        binary_10bit = B;
        success = true;
        
    catch ME
        fprintf('全加器链式累加算法错误: %s\n', ME.message);
        success = false;
        binary_10bit = zeros(1, 10);
    end
end

function [sum_out, carry_out] = standard_full_adder(A, B, carry_in)
%STANDARD_FULL_ADDER 标准全加器逻辑
%   输入：A, B, Carry_in (0或1)
%   输出：Sum, Carry_out (0或1)
%   逻辑：A + B + Cin = Sum + 2×Cout

    % 确保输入为0或1
    A = round(max(0, min(1, A)));
    B = round(max(0, min(1, B)));
    carry_in = round(max(0, min(1, carry_in)));
    
    % 全加器逻辑
    temp_sum = A + B + carry_in;
    sum_out = mod(temp_sum, 2);      % Sum = (A + B + Cin) mod 2
    carry_out = floor(temp_sum / 2); % Cout = floor((A + B + Cin) / 2)
end

function [analog_value, success] = execute_ideal_dac_conversion(binary_10bit, Vref)
%EXECUTE_IDEAL_DAC_CONVERSION 执行理想DAC转换
%   实现标准权重赋值、偏移和归一化流程
%   
%   算法流程：
%   1. 权重累加：weighted_sum = B0×512 + B1×256 + ... + B8×2 + B9×1
%   2. 偏移处理：offset = -(2^10-1)/2 = -511.5
%   3. 归一化：analog_output = (weighted_sum + offset) × (1/512) × Vref
    
    try
        % 检查输入数据有效性
        if length(binary_10bit) ~= 10
            error('输入数据必须为10位二进制码');
        end
        
        % 确保输入为二进制位（0或1）
        B = round(binary_10bit);
        B = max(0, min(1, B));  % 限制在[0,1]范围内
        
        % 第一步：权重赋值
        % DAC权重：[512, 256, 128, 64, 32, 16, 8, 4, 2, 1]
        weights = 2.^(9:-1:0);  % B0×512, B1×256, ..., B8×2, B9×1
        
        % 权重累加
        weighted_sum = sum(B .* weights);
        
        % 第二步：偏移处理
        % offset = -(2^10-1)/2 = -511.5
        offset = -(2^10-1)/2;
        processed_sum = weighted_sum + offset;
        
        % 第三步：归一化操作
        % 除以512并乘以参考电压
        normalized_value = processed_sum * (1/512);
        analog_value = normalized_value * Vref;
        
        % 饱和保护：限制在[-Vref, +Vref]范围内
        analog_value = max(-Vref, min(Vref, analog_value));
        
        success = true;
        
    catch ME
        fprintf('理想DAC转换错误: %s\n', ME.message);
        success = false;
        analog_value = 0;
    end
end 
function [correction_output_struct, correction_metrics] = ...
    digital_correction_v4(digital_data_struct, params)
%DIGITAL_CORRECTION_V4 数字校正模块 (v4版本) - 标准全加器链式校正
%   VERSION HISTORY:
%   v1-v3: 基于stage_history的复杂标签对齐机制和RSD校正
%   v4: 基于数据矩阵的时延对齐和标准全加器链式校正重构版本
%   v4.1: 简化版本 - 延迟对齐迁移到事件驱动核心
%   v4.2: 统一结构体输出格式 - 参考generate_timebase.m的数据结构设计
%
%   v4.2版本核心特性:
%   1. 处理预对齐的数字数据结构体 (18位独立存储)
%   2. 延迟对齐已在事件驱动核心完成，无需重复处理
%   3. 直接执行标准全加器链式校正：18bit → 全加器链 → 10bit
%   4. 理想DAC转换：权重赋值、偏移处理、归一化操作
%   5. 统一结构体输出：1个模拟信号 + 10个数字信号
%   6. 与generate_timebase.m保持一致的数据结构格式
%
%   输入参数:
%       digital_data_struct - 数字数据结构体 (18位独立存储)
%       params - ADC参数
%
%   输出参数:
%       correction_output_struct - 校正输出结构体
%           .time - 时间向量 (列向量)
%           .signals - 信号结构体数组 (1个模拟 + 10个数字)
%       correction_metrics - 校正性能统计

    fprintf('=== 数字校正模块 v4.2 (统一结构体输出格式) ===\n');
    
    %% 初始化校正系统
    % 从数字数据结构体获取样本数量和时间信息
    num_samples = length(digital_data_struct.signals(1).values);
    time_vector = digital_data_struct.time;  % 获取时间向量
    fs = params.fs;
    
    % 初始化校正统计
    correction_metrics = struct();
    correction_metrics.total_samples = num_samples;
    correction_metrics.fs = fs;
    correction_metrics.alignment_steps = [];
    correction_metrics.chain_corrections = 0;
    correction_metrics.processing_efficiency = 0;

    %% 直接执行标准全加器链式校正算法（延迟对齐已在事件驱动核心完成）
    fprintf('\n=== 标准全加器链式校正算法（基于预对齐数据） ===\n');
    [corrected_analog_output, corrected_binary_output] = ...
        perform_rsd_correction(digital_data_struct, params);
    
    %% 创建统一输出结构体（参考generate_timebase.m格式）
    fprintf('\n=== 创建统一输出结构体 ===\n');
    correction_output_struct = create_correction_output_struct(...
        time_vector, corrected_analog_output, corrected_binary_output, num_samples);
    
    %% 生成校正统计
    correction_metrics.chain_corrections = sum(abs(corrected_analog_output) > 1e-10);
    correction_metrics.processing_efficiency = correction_metrics.chain_corrections / num_samples;
    
    fprintf('\n=== 数字校正v4.2完成 ===\n');
    fprintf('校正输出: %d/%d样本 (%.1f%%)\n', ...
            correction_metrics.chain_corrections, num_samples, ...
            correction_metrics.processing_efficiency * 100);
    fprintf('输出结构体: %d个信号 (1个模拟 + 10个数字)\n', ...
            length(correction_output_struct.signals));
end

function [corrected_analog_output, corrected_binary_output] = ...
    perform_rsd_correction(digital_data_struct, params)
%PERFORM_STANDARD_CORRECTION_DIRECT 直接执行标准全加器链式校正算法（基于预对齐数据）
%   输入的数字数据结构体已经在事件驱动核心中完成了延迟对齐
%   实现完整的18bit→全加器链→10bit→理想DAC流程

    fprintf('执行标准全加器链式校正算法 (18bit → 10bit，基于预对齐数据)...\n');

    % 获取数据维度
    num_samples = length(digital_data_struct.signals(1).values);

    % 初始化输出
    corrected_analog_output = zeros(num_samples, 1);
    corrected_binary_output = zeros(num_samples, 10);

    % 数字校正参数
    Vref = params.Vref;

    %% 逐样本执行标准全加器链式校正
    valid_count = 0;

    for sample_idx = 1:num_samples
        % 提取当前样本的18bit数字码（直接从结构体读取）
        sample_18bit = zeros(18, 1);
        for bit_idx = 1:18
            sample_18bit(bit_idx) = digital_data_struct.signals(bit_idx).values(sample_idx);
        end

        % 处理所有样本（包括全0样本，因为全0也是有效的数字输入）
        if true  % 处理所有样本
            % 执行标准全加器链式累加算法
            [binary_10bit, chain_success] = execute_standard_full_adder_chain(sample_18bit);

            if chain_success
                % 执行理想DAC转换（权重赋值、偏移和归一化）
                [analog_value, dac_success] = execute_ideal_dac_conversion(binary_10bit, Vref);

                if dac_success
                    corrected_binary_output(sample_idx, :) = binary_10bit;
                    corrected_analog_output(sample_idx) = analog_value;
                    valid_count = valid_count + 1;
                end
            end
        end

        % 进度显示（每10000个样本）
        if mod(sample_idx, 10000) == 0
            fprintf('全加器链式校正进度: %d/%d样本 (%.1f%%)\n', ...
                    sample_idx, num_samples, 100*sample_idx/num_samples);
        end
    end

    fprintf('全加器链式校正完成: 成功校正%d/%d样本 (%.1f%%)\n', ...
            valid_count, num_samples, 100*valid_count/num_samples);
end

function [binary_10bit, success] = execute_standard_full_adder_chain(sample_18bit)
%EXECUTE_STANDARD_FULL_ADDER_CHAIN 执行标准全加器链式累加算法
%   实现18bit独立数字位到10bit二进制码的标准转换流程
%   
%   算法流程：
%   输入：D0(MSB,STAGE1) → D17(LSB,Flash ADC)
%   输出：B0(MSB) → B9(LSB)
%   
%   全加器链：
%   B9 = D17                           (直接赋值)
%   D16 + D15 + 0    = B8 + C8        (全加器1)
%   D14 + D13 + C8   = B7 + C7        (全加器2)
%   D12 + D11 + C7   = B6 + C6        (全加器3)
%   D10 + D9  + C6   = B5 + C5        (全加器4)
%   D8  + D7  + C5   = B4 + C4        (全加器5)
%   D6  + D5  + C4   = B3 + C3        (全加器6)
%   D4  + D3  + C3   = B2 + C2        (全加器7)
%   D2  + D1  + C2   = B1 + C1        (全加器8)
%   D0  + 0   + C1   = B0 + C0(丢弃)   (全加器9)
    
    try
        % 检查输入数据有效性
        if length(sample_18bit) ~= 18
            error('输入数据必须为18位');
        end
        
        % 确保输入为二进制位（0或1）
        D = round(sample_18bit);  % D(1)=D0, D(2)=D1, ..., D(18)=D17
        D = max(0, min(1, D));    % 限制在[0,1]范围内
        
        % 初始化输出位和进位
        B = zeros(1, 10);  % B(1)=B0, B(2)=B1, ..., B(10)=B9
        C = 0;             % 初始进位为0
        
        % 第一步：B9 = D17 (直接赋值，最低位)
        B(10) = D(18);     % B9 = D17
        
        % 第二步：全加器链式累加 (从低位到高位)
        % 全加器1: D16 + D15 + 0 = B8 + C8
        [B(9), C] = standard_full_adder(D(17), D(16), C);
        
        % 全加器2: D14 + D13 + C8 = B7 + C7
        [B(8), C] = standard_full_adder(D(15), D(14), C);
        
        % 全加器3: D12 + D11 + C7 = B6 + C6
        [B(7), C] = standard_full_adder(D(13), D(12), C);
        
        % 全加器4: D10 + D9 + C6 = B5 + C5
        [B(6), C] = standard_full_adder(D(11), D(10), C);
        
        % 全加器5: D8 + D7 + C5 = B4 + C4
        [B(5), C] = standard_full_adder(D(9), D(8), C);
        
        % 全加器6: D6 + D5 + C4 = B3 + C3
        [B(4), C] = standard_full_adder(D(7), D(6), C);
        
        % 全加器7: D4 + D3 + C3 = B2 + C2
        [B(3), C] = standard_full_adder(D(5), D(4), C);
        
        % 全加器8: D2 + D1 + C2 = B1 + C1
        [B(2), C] = standard_full_adder(D(3), D(2), C);
        
        % 全加器9: D0 + 0 + C1 = B0 + C0(丢弃)
        [B(1), ~] = standard_full_adder(D(1), 0, C);
        
        binary_10bit = B;
        success = true;
        
    catch ME
        fprintf('全加器链式累加算法错误: %s\n', ME.message);
        success = false;
        binary_10bit = zeros(1, 10);
    end
end

function [sum_out, carry_out] = standard_full_adder(A, B, carry_in)
%STANDARD_FULL_ADDER 标准全加器逻辑
%   输入：A, B, Carry_in (0或1)
%   输出：Sum, Carry_out (0或1)
%   逻辑：A + B + Cin = Sum + 2×Cout

    % 确保输入为0或1
    A = round(max(0, min(1, A)));
    B = round(max(0, min(1, B)));
    carry_in = round(max(0, min(1, carry_in)));
    
    % 全加器逻辑
    temp_sum = A + B + carry_in;
    sum_out = mod(temp_sum, 2);      % Sum = (A + B + Cin) mod 2
    carry_out = floor(temp_sum / 2); % Cout = floor((A + B + Cin) / 2)
end

function [analog_value, success] = execute_ideal_dac_conversion(binary_10bit, Vref)
%EXECUTE_IDEAL_DAC_CONVERSION 执行理想DAC转换
%   实现标准权重赋值、偏移和归一化流程
%   
%   算法流程：
%   1. 权重累加：weighted_sum = B0×512 + B1×256 + ... + B8×2 + B9×1
%   2. 偏移处理：offset = -(2^10-1)/2 = -511.5
%   3. 归一化：analog_output = (weighted_sum + offset) × (1/512) × Vref
    
    try
        % 检查输入数据有效性
        if length(binary_10bit) ~= 10
            error('输入数据必须为10位二进制码');
        end
        
        % 确保输入为二进制位（0或1）
        B = round(binary_10bit);
        B = max(0, min(1, B));  % 限制在[0,1]范围内
        
        % 第一步：权重赋值
        % DAC权重：[512, 256, 128, 64, 32, 16, 8, 4, 2, 1]
        weights = 2.^(9:-1:0);  % B0×512, B1×256, ..., B8×2, B9×1
        
        % 权重累加
        weighted_sum = sum(B .* weights);
        
        % 第二步：偏移处理
        % offset = -(2^10-1)/2 = -511.5
        offset = -(2^10-1)/2;
        processed_sum = weighted_sum + offset;
        
        % 第三步：归一化操作
        % 除以512并乘以参考电压
        normalized_value = processed_sum * (1/512);
        analog_value = normalized_value * Vref;
        
        % 饱和保护：限制在[-Vref, +Vref]范围内
        analog_value = max(-Vref, min(Vref, analog_value));
        
        success = true;
        
    catch ME
        fprintf('理想DAC转换错误: %s\n', ME.message);
        success = false;
        analog_value = 0;
    end
end

function correction_output_struct = create_correction_output_struct(...
    time_vector, corrected_analog_output, corrected_binary_output, num_samples)
%CREATE_CORRECTION_OUTPUT_STRUCT 创建校正输出结构体
%   严格按照generate_timebase.m的结构体格式创建输出数据结构
%   
%   输入参数:
%       time_vector - 时间向量
%       corrected_analog_output - 校正后的模拟输出 (N×1)
%       corrected_binary_output - 校正后的10bit数字输出 (N×10)
%       num_samples - 样本数量
%
%   输出参数:
%       correction_output_struct - 校正输出结构体
%           .time - 时间向量 (列向量)
%           .signals - 信号结构体数组 (1个模拟 + 10个数字)

    fprintf('创建校正输出结构体 (参考generate_timebase.m格式)...\n');
    
    % 创建基础结构体
    correction_output_struct = struct();
    
    % 添加时间向量 (确保为列向量)
    correction_output_struct.time = time_vector(:);
    
    % 初始化信号结构体数组
    correction_signals = [];
    
    %% 创建模拟输出信号结构体
    analog_signal = struct();
    analog_signal.values = corrected_analog_output(:);  % 确保为列向量
    analog_signal.dimensions = 1;                       % 单维信号
    analog_signal.label = 'Corrected_Analog_Output';    % 信号标签
    analog_signal.blockName = 'DigitalCorrection/AnalogOutput';  % 模块路径
    
    correction_signals = analog_signal;
    
    %% 创建10个数字输出信号结构体 (B0到B9)
    for bit_idx = 1:10
        digital_signal = struct();
        digital_signal.values = corrected_binary_output(:, bit_idx);  % 提取对应位，确保为列向量
        digital_signal.dimensions = 1;                               % 单维信号
        digital_signal.label = sprintf('B%d', bit_idx-1);            % B0(MSB)到B9(LSB)
        digital_signal.blockName = sprintf('DigitalCorrection/B%d', bit_idx-1);  % 模块路径
        
        correction_signals(end+1) = digital_signal;
    end
    
    correction_output_struct.signals = correction_signals;
    
    % 验证结构体格式
    total_signals = length(correction_output_struct.signals);
    fprintf('  结构体创建完成: %d个信号 (1个模拟 + %d个数字)\n', total_signals, total_signals-1);
    fprintf('  时间向量大小: %d×%d\n', size(correction_output_struct.time, 1), size(correction_output_struct.time, 2));
    fprintf('  模拟信号大小: %d×%d\n', size(correction_output_struct.signals(1).values, 1), size(correction_output_struct.signals(1).values, 2));
    fprintf('  数字信号大小: %d×%d (每个数字位)\n', size(correction_output_struct.signals(2).values, 1), size(correction_output_struct.signals(2).values, 2));
    
    % 数据完整性检查
    if length(correction_output_struct.time) ~= num_samples
        warning('时间向量长度不匹配: 期望%d，实际%d', num_samples, length(correction_output_struct.time));
    end
    
    for signal_idx = 1:total_signals
        if length(correction_output_struct.signals(signal_idx).values) ~= num_samples
            warning('信号%d长度不匹配: 期望%d，实际%d', signal_idx, num_samples, length(correction_output_struct.signals(signal_idx).values));
        end
    end
    
    fprintf('  数据完整性检查通过\n');
end 